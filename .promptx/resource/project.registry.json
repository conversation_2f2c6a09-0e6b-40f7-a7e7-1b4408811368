{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T13:19:41.239Z", "updatedAt": "2025-08-01T13:19:41.254Z", "resourceCount": 34}, "resources": [{"id": "banner", "source": "project", "protocol": "role", "name": "Banner 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/banner/banner.role.md", "metadata": {"createdAt": "2025-08-01T13:19:41.240Z", "updatedAt": "2025-08-01T13:19:41.240Z", "scannedAt": "2025-08-01T13:19:41.240Z", "path": "role/banner/banner.role.md"}}, {"id": "ears-specification", "source": "project", "protocol": "execution", "name": "Ears Specification 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/banner/execution/ears-specification.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.240Z", "updatedAt": "2025-08-01T13:19:41.240Z", "scannedAt": "2025-08-01T13:19:41.240Z", "path": "role/banner/execution/ears-specification.execution.md"}}, {"id": "requirements-analysis", "source": "project", "protocol": "execution", "name": "Requirements Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/banner/execution/requirements-analysis.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.241Z", "updatedAt": "2025-08-01T13:19:41.241Z", "scannedAt": "2025-08-01T13:19:41.241Z", "path": "role/banner/execution/requirements-analysis.execution.md"}}, {"id": "shrimp-task-integration", "source": "project", "protocol": "execution", "name": "Shrimp Task Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/shrimp-task-integration.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.250Z", "updatedAt": "2025-08-01T13:19:41.250Z", "scannedAt": "2025-08-01T13:19:41.250Z", "path": "role/stark/execution/shrimp-task-integration.execution.md"}}, {"id": "analytical-thinking", "source": "project", "protocol": "thought", "name": "Analytical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/banner/thought/analytical-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.241Z", "updatedAt": "2025-08-01T13:19:41.241Z", "scannedAt": "2025-08-01T13:19:41.241Z", "path": "role/banner/thought/analytical-thinking.thought.md"}}, {"id": "risk-assessment", "source": "project", "protocol": "thought", "name": "Risk Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/risk-assessment.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.244Z", "updatedAt": "2025-08-01T13:19:41.244Z", "scannedAt": "2025-08-01T13:19:41.244Z", "path": "role/black-widow/thought/risk-assessment.thought.md"}}, {"id": "black-widow", "source": "project", "protocol": "role", "name": "Black Widow 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/black-widow/black-widow.role.md", "metadata": {"createdAt": "2025-08-01T13:19:41.242Z", "updatedAt": "2025-08-01T13:19:41.242Z", "scannedAt": "2025-08-01T13:19:41.242Z", "path": "role/black-widow/black-widow.role.md"}}, {"id": "intelligence-workflow", "source": "project", "protocol": "execution", "name": "Intelligence Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/intelligence-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.243Z", "updatedAt": "2025-08-01T13:19:41.243Z", "scannedAt": "2025-08-01T13:19:41.243Z", "path": "role/black-widow/execution/intelligence-workflow.execution.md"}}, {"id": "interaction-and-results", "source": "project", "protocol": "execution", "name": "Interaction And Results 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/black-widow/execution/interaction-and-results.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.243Z", "updatedAt": "2025-08-01T13:19:41.243Z", "scannedAt": "2025-08-01T13:19:41.243Z", "path": "role/black-widow/execution/interaction-and-results.execution.md"}}, {"id": "tool-orchestration", "source": "project", "protocol": "execution", "name": "Tool Orchestration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/tool-orchestration.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.248Z", "updatedAt": "2025-08-01T13:19:41.248Z", "scannedAt": "2025-08-01T13:19:41.248Z", "path": "role/pepper/execution/tool-orchestration.execution.md"}}, {"id": "intelligence-analysis", "source": "project", "protocol": "thought", "name": "Intelligence Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/intelligence-analysis.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.243Z", "updatedAt": "2025-08-01T13:19:41.243Z", "scannedAt": "2025-08-01T13:19:41.243Z", "path": "role/black-widow/thought/intelligence-analysis.thought.md"}}, {"id": "pattern-recognition", "source": "project", "protocol": "thought", "name": "Pattern Recognition 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/black-widow/thought/pattern-recognition.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.244Z", "updatedAt": "2025-08-01T13:19:41.244Z", "scannedAt": "2025-08-01T13:19:41.244Z", "path": "role/black-widow/thought/pattern-recognition.thought.md"}}, {"id": "dialogue-management", "source": "project", "protocol": "execution", "name": "Dialogue Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/dialogue-management.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.245Z", "updatedAt": "2025-08-01T13:19:41.245Z", "scannedAt": "2025-08-01T13:19:41.245Z", "path": "role/fury/execution/dialogue-management.execution.md"}}, {"id": "fury-workflow", "source": "project", "protocol": "execution", "name": "Fury Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/fury-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.245Z", "updatedAt": "2025-08-01T13:19:41.245Z", "scannedAt": "2025-08-01T13:19:41.245Z", "path": "role/fury/execution/fury-workflow.execution.md"}}, {"id": "resume-generation", "source": "project", "protocol": "execution", "name": "Resume Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fury/execution/resume-generation.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.245Z", "updatedAt": "2025-08-01T13:19:41.245Z", "scannedAt": "2025-08-01T13:19:41.245Z", "path": "role/fury/execution/resume-generation.execution.md"}}, {"id": "fury", "source": "project", "protocol": "role", "name": "Fury 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fury/fury.role.md", "metadata": {"createdAt": "2025-08-01T13:19:41.246Z", "updatedAt": "2025-08-01T13:19:41.246Z", "scannedAt": "2025-08-01T13:19:41.246Z", "path": "role/fury/fury.role.md"}}, {"id": "agent-broker-mindset", "source": "project", "protocol": "thought", "name": "Agent Broker Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/agent-broker-mindset.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.247Z", "updatedAt": "2025-08-01T13:19:41.247Z", "scannedAt": "2025-08-01T13:19:41.247Z", "path": "role/fury/thought/agent-broker-mindset.thought.md"}}, {"id": "value-discovery-techniques", "source": "project", "protocol": "thought", "name": "Value Discovery Techniques 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fury/thought/value-discovery-techniques.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.247Z", "updatedAt": "2025-08-01T13:19:41.247Z", "scannedAt": "2025-08-01T13:19:41.247Z", "path": "role/fury/thought/value-discovery-techniques.thought.md"}}, {"id": "core-management", "source": "project", "protocol": "execution", "name": "Core Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/core-management.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.248Z", "updatedAt": "2025-08-01T13:19:41.248Z", "scannedAt": "2025-08-01T13:19:41.248Z", "path": "role/pepper/execution/core-management.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-08-01T13:19:41.249Z", "updatedAt": "2025-08-01T13:19:41.249Z", "scannedAt": "2025-08-01T13:19:41.249Z", "path": "role/pepper/pepper.role.md"}}, {"id": "adaptive-learning", "source": "project", "protocol": "thought", "name": "Adaptive Learning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/adaptive-learning.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.249Z", "updatedAt": "2025-08-01T13:19:41.249Z", "scannedAt": "2025-08-01T13:19:41.249Z", "path": "role/pepper/thought/adaptive-learning.thought.md"}}, {"id": "verification-mindset", "source": "project", "protocol": "thought", "name": "Verification Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/verification-mindset.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.249Z", "updatedAt": "2025-08-01T13:19:41.249Z", "scannedAt": "2025-08-01T13:19:41.249Z", "path": "role/pepper/thought/verification-mindset.thought.md"}}, {"id": "architecture-design", "source": "project", "protocol": "execution", "name": "Architecture Design 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/architecture-design.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.250Z", "updatedAt": "2025-08-01T13:19:41.250Z", "scannedAt": "2025-08-01T13:19:41.250Z", "path": "role/stark/execution/architecture-design.execution.md"}}, {"id": "technology-selection", "source": "project", "protocol": "execution", "name": "Technology Selection 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stark/execution/technology-selection.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.250Z", "updatedAt": "2025-08-01T13:19:41.250Z", "scannedAt": "2025-08-01T13:19:41.250Z", "path": "role/stark/execution/technology-selection.execution.md"}}, {"id": "stark", "source": "project", "protocol": "role", "name": "Stark 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/stark/stark.role.md", "metadata": {"createdAt": "2025-08-01T13:19:41.251Z", "updatedAt": "2025-08-01T13:19:41.251Z", "scannedAt": "2025-08-01T13:19:41.251Z", "path": "role/stark/stark.role.md"}}, {"id": "architectural-thinking", "source": "project", "protocol": "thought", "name": "Architectural Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stark/thought/architectural-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.251Z", "updatedAt": "2025-08-01T13:19:41.251Z", "scannedAt": "2025-08-01T13:19:41.251Z", "path": "role/stark/thought/architectural-thinking.thought.md"}}, {"id": "technology-evaluation", "source": "project", "protocol": "thought", "name": "Technology Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stark/thought/technology-evaluation.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.251Z", "updatedAt": "2025-08-01T13:19:41.251Z", "scannedAt": "2025-08-01T13:19:41.251Z", "path": "role/stark/thought/technology-evaluation.thought.md"}}, {"id": "vision-document-management", "source": "project", "protocol": "execution", "name": "Vision Document Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-document-management.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.252Z", "updatedAt": "2025-08-01T13:19:41.252Z", "scannedAt": "2025-08-01T13:19:41.252Z", "path": "role/vision/execution/vision-document-management.execution.md"}}, {"id": "vision-enhanced-task-workflow", "source": "project", "protocol": "execution", "name": "Vision Enhanced Task Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vision/execution/vision-enhanced-task-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T13:19:41.252Z", "updatedAt": "2025-08-01T13:19:41.252Z", "scannedAt": "2025-08-01T13:19:41.252Z", "path": "role/vision/execution/vision-enhanced-task-workflow.execution.md"}}, {"id": "shrimp-task-manager-tools", "source": "project", "protocol": "knowledge", "name": "Shrimp Task Manager Tools 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/shrimp-task-manager-tools.knowledge.md", "metadata": {"createdAt": "2025-08-01T13:19:41.253Z", "updatedAt": "2025-08-01T13:19:41.253Z", "scannedAt": "2025-08-01T13:19:41.253Z", "path": "role/vision/knowledge/shrimp-task-manager-tools.knowledge.md"}}, {"id": "zhi-interaction-protocol", "source": "project", "protocol": "knowledge", "name": "Zhi Interaction Protocol 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/vision/knowledge/zhi-interaction-protocol.knowledge.md", "metadata": {"createdAt": "2025-08-01T13:19:41.253Z", "updatedAt": "2025-08-01T13:19:41.253Z", "scannedAt": "2025-08-01T13:19:41.253Z", "path": "role/vision/knowledge/zhi-interaction-protocol.knowledge.md"}}, {"id": "vision-analytical-mind", "source": "project", "protocol": "thought", "name": "Vision Analytical Mind 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-analytical-mind.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.253Z", "updatedAt": "2025-08-01T13:19:41.253Z", "scannedAt": "2025-08-01T13:19:41.253Z", "path": "role/vision/thought/vision-analytical-mind.thought.md"}}, {"id": "vision-task-strategy", "source": "project", "protocol": "thought", "name": "Vision Task Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vision/thought/vision-task-strategy.thought.md", "metadata": {"createdAt": "2025-08-01T13:19:41.254Z", "updatedAt": "2025-08-01T13:19:41.254Z", "scannedAt": "2025-08-01T13:19:41.254Z", "path": "role/vision/thought/vision-task-strategy.thought.md"}}, {"id": "vision", "source": "project", "protocol": "role", "name": "Vision 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/vision/vision.role.md", "metadata": {"createdAt": "2025-08-01T13:19:41.254Z", "updatedAt": "2025-08-01T13:19:41.254Z", "scannedAt": "2025-08-01T13:19:41.254Z", "path": "role/vision/vision.role.md"}}], "stats": {"totalResources": 34, "byProtocol": {"role": 6, "execution": 14, "thought": 12, "knowledge": 2}, "bySource": {"project": 34}}}