<execution>
  <constraint>
    ## Shrimp任务系统技术约束
    - **工具强制使用**：必须使用shrimp-video-factory工具链进行任务管理
    - **质量门禁约束**：所有任务必须通过verify_task_shrimp-video-factory验证，≥80分
    - **依赖关系约束**：必须建立与后续角色的任务依赖关系
    - **状态同步约束**：任务状态必须与实际工作进度保持同步
  </constraint>

  <rule>
    ## 任务管理强制规则
    - **任务粒度控制**：每个子任务应在1-2个工作天内完成
    - **验证强制执行**：每个任务完成后必须立即验证质量
    - **状态更新强制**：任务状态变更必须及时同步到系统
    - **依赖管理强制**：必须正确建立任务间的依赖关系
    - **文档同步强制**：任务完成必须同步更新相关文档
  </rule>

  <guideline>
    ## 任务管理指导原则
    - **渐进式完成**：按优先级和依赖关系逐步完成任务
    - **质量优先**：宁可延期也要确保质量达标
    - **透明协作**：通过任务系统保持协作透明
    - **经验积累**：将重要经验记录到任务系统
    - **持续改进**：基于任务执行反馈优化流程
  </guideline>

  <process>
    ## Banner角色任务管理标准流程
    
    ### 阶段1：项目任务初始化
    ```bash
    # 1. 创建项目主任务
    plan_task_shrimp-video-factory "项目需求分析：[项目名称] - 深度挖掘用户需求，编写完整需求文档"
    
    # 任务描述模板：
    # - 项目背景：[简要描述项目背景和目标]
    # - 分析范围：[明确需求分析的范围和边界]
    # - 交付标准：[明确交付物和质量标准]
    # - 时间要求：[预期完成时间和关键节点]
    
    # 2. 初始化项目规范（新项目）
    init_project_rules_shrimp-video-factory
    ```
    
    ### 阶段2：需求分析任务分解
    ```bash
    # 1. 分解主任务为子任务
    split_tasks_shrimp-video-factory [主任务ID]
    
    # 标准分解结构：
    # ├── 需求挖掘任务
    # │   ├── 用户访谈和需求收集
    # │   ├── 业务流程分析
    # │   ├── 功能需求识别
    # │   └── 非功能需求分析
    # ├── 可行性分析任务
    # │   ├── 技术可行性评估
    # │   ├── 资源需求分析
    # │   └── 时间成本估算
    # ├── 风险评估任务
    # │   ├── 业务风险识别
    # │   ├── 技术风险评估
    # │   └── 应对策略制定
    # ├── 文档编写任务
    # │   ├── EARS格式转换
    # │   ├── requirements.md编写
    # │   └── 验收标准定义
    # └── 用户确认任务
    #     ├── 需求确认会议
    #     └── 文档签字确认
    
    # 2. 查看任务分解结果
    list_tasks_shrimp-video-factory pending
    get_task_detail_shrimp-video-factory [子任务ID]
    ```
    
    ### 阶段3：任务执行和质量控制
    ```bash
    # 1. 按优先级执行任务
    execute_task_shrimp-video-factory [需求挖掘任务ID]
    # 执行指导：
    # - 使用结构化提问技巧
    # - 记录所有重要信息
    # - 识别关键需求和约束
    # - 建立需求优先级
    
    # 2. 立即验证任务质量
    verify_task_shrimp-video-factory [需求挖掘任务ID]
    # 验证标准：
    # - 需求完整性：是否覆盖所有重要需求
    # - 需求准确性：是否准确理解用户意图
    # - 文档质量：是否符合格式和标准要求
    # - 可操作性：是否为后续工作提供清晰基础
    
    # 3. 更新任务状态和成果
    update_task_shrimp-video-factory [需求挖掘任务ID] --summary "需求挖掘完成，识别核心需求X个，关键约束Y个"
    ```
    
    ### 阶段4：复杂问题深度思考
    ```bash
    # 1. 对复杂需求进行深度分析
    analyze_task_shrimp-video-factory [复杂需求任务ID]
    # 分析重点：
    # - 需求复杂度评估
    # - 实现难点识别
    # - 风险点分析
    # - 解决方案建议
    
    # 2. 使用思考工具处理复杂问题
    process_thought_shrimp-video-factory "需求复杂度分析：[具体问题描述]"
    # 思考维度：
    # - 问题本质分析
    # - 多种解决方案比较
    # - 风险和收益评估
    # - 最优方案推荐
    
    # 3. 反思和优化分析结果
    reflect_task_shrimp-video-factory [分析结果]
    # 反思重点：
    # - 分析是否全面深入
    # - 是否遗漏重要因素
    # - 结论是否合理可行
    # - 是否需要进一步分析
    ```
    
    ### 阶段5：协作交接和依赖建立
    ```bash
    # 1. 完成所有需求分析任务
    list_tasks_shrimp-video-factory pending  # 确认无待完成任务
    
    # 2. 最终质量验证
    verify_task_shrimp-video-factory [主任务ID]
    # 整体验证标准：
    # - 所有子任务100%完成
    # - 质量验证全部通过
    # - 文档完整且符合标准
    # - 为后续工作提供清晰基础
    
    # 3. 建立与Stark的任务依赖
    # 系统会自动为Stark角色创建架构设计任务
    # 并建立对当前需求分析任务的依赖关系
    
    # 4. 更新主任务状态
    update_task_shrimp-video-factory [主任务ID] --summary "需求分析完成，生成01-requirements.md，为Stark建立架构设计依赖"
    ```
    
    ### 阶段6：知识记录和经验沉淀
    ```bash
    # 1. 记录重要的需求模式
    promptx_remember "项目[X]需求特征：[关键特征描述]，适用模式：[分析模式]"
    
    # 2. 记录用户偏好和约束
    promptx_remember "用户[Y]偏好：[具体偏好]，技术约束：[约束条件]"
    
    # 3. 记录成功的分析方法
    promptx_remember "需求类型[Z]最佳分析方法：[方法描述]，关键要点：[要点列表]"
    
    # 4. 查看项目整体进度
    list_tasks_shrimp-video-factory all
    query_task_shrimp-video-factory "completed"
    ```
  </process>

  <criteria>
    ## 任务管理质量标准
    
    ### 任务执行质量标准
    - ✅ **完成率**：所有分解任务100%完成
    - ✅ **质量通过率**：所有任务通过verify验证，≥80分
    - ✅ **时效性**：任务在预定时间内完成
    - ✅ **依赖准确性**：任务依赖关系建立正确
    
    ### 协作效率标准
    - ✅ **信息传递准确性**：任务信息准确传递给后续角色
    - ✅ **工作基础完整性**：为后续工作提供完整基础
    - ✅ **问题预防效果**：有效预防后续阶段的需求问题
    - ✅ **知识传承效果**：重要经验成功记录和传承
    
    ### 系统集成质量标准
    - ✅ **工具使用规范性**：正确使用所有shrimp工具
    - ✅ **状态同步准确性**：任务状态与实际进度100%同步
    - ✅ **文档关联性**：任务与文档保持一致关联
    - ✅ **流程标准化**：严格按照标准流程执行
  </criteria>
</execution>
