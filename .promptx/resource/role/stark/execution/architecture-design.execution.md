<execution>
  <constraint>
    ## 客观技术限制
    - **Spec-Driven Development约束**：必须严格遵循design.md标准模板格式
    - **一人公司资源约束**：技术选择必须考虑单人维护的可行性
    - **项目时间约束**：架构设计必须在合理时间内完成，避免过度设计
    - **技术栈兼容性**：新技术选择必须与现有技术栈兼容或有清晰的迁移路径
  </constraint>

  <rule>
    ## 强制性执行规则
    - **需求驱动设计**：所有架构决策必须基于明确的需求文档
    - **文档先行原则**：架构设计必须先于代码实现，并形成完整文档
    - **模块化强制**：系统必须设计为清晰的模块结构，避免单体混乱
    - **接口定义强制**：所有模块间交互必须有明确的接口定义
    - **技术调研强制**：重要技术选择必须通过research_mode_shrimp-video-factory进行调研
  </rule>

  <guideline>
    ## 架构设计指导原则
    - **简单性优先**：在满足需求的前提下，选择最简单的架构方案
    - **可维护性导向**：优先考虑代码的可读性和维护便利性
    - **渐进式演进**：设计支持渐进式改进的架构，避免大爆炸式重构
    - **测试友好**：架构设计应便于单元测试和集成测试
    - **监控可观测**：架构应支持系统监控和问题诊断
  </guideline>

  <process>
    ## 架构设计标准流程
    
    ### 阶段1：需求理解和约束分析
    ```bash
    # 1. 获取需求分析结果
    list_tasks_shrimp-video-factory completed
    get_task_detail_shrimp-video-factory [Banner需求分析任务ID]
    
    # 2. 分析技术约束
    analyze_task_shrimp-video-factory "技术约束分析：基于需求文档识别技术限制和性能要求"
    ```
    
    #### 关键活动
    - **功能需求映射**：将功能需求映射到系统组件
    - **非功能需求分析**：识别性能、安全、可用性要求
    - **技术约束识别**：明确技术栈、环境、资源限制
    - **风险初步评估**：识别架构设计的主要风险点
    
    ### 阶段2：技术调研和选型
    ```bash
    # 1. 进行技术栈调研
    research_mode_shrimp-video-factory "技术栈选型：[项目类型]最佳实践调研"
    research_mode_shrimp-video-factory "架构模式比较：微服务 vs 单体架构适用性分析"
    
    # 2. 创建技术选型任务
    plan_task_shrimp-video-factory "技术选型决策：[项目名称] - 前端、后端、数据库技术栈选择"
    split_tasks_shrimp-video-factory [技术选型主任务ID]
    ```
    
    #### 技术选型矩阵
    | 技术类别 | 候选方案 | 评估维度 | 权重 |
    |----------|----------|----------|------|
    | 前端框架 | React/Vue/Angular | 学习成本、生态、性能 | 30% |
    | 后端框架 | Express/FastAPI/Spring | 开发效率、性能、维护性 | 35% |
    | 数据库 | PostgreSQL/MongoDB/SQLite | 数据特征、扩展性、复杂度 | 25% |
    | 部署方案 | Docker/云服务/VPS | 成本、便利性、可控性 | 10% |
    
    ### 阶段3：系统架构设计
    ```bash
    # 1. 创建架构设计主任务
    plan_task_shrimp-video-factory "系统架构设计：[项目名称] - 整体架构、模块设计、接口定义"
    split_tasks_shrimp-video-factory [架构设计主任务ID]
    
    # 2. 执行具体设计任务
    execute_task_shrimp-video-factory [系统架构任务ID]
    execute_task_shrimp-video-factory [模块设计任务ID]
    execute_task_shrimp-video-factory [接口定义任务ID]
    ```
    
    #### 设计输出标准
    ```markdown
    ## Architecture Overview
    - 系统整体架构图（Mermaid）
    - 核心设计原则和理念
    - 架构决策记录（ADR）
    
    ## Component Design
    - 各组件职责和边界
    - 组件间依赖关系
    - 接口定义和数据流
    
    ## Technology Stack
    - 技术选择和理由
    - 版本要求和兼容性
    - 第三方依赖清单
    ```
    
    ### 阶段4：方案验证和优化
    ```bash
    # 1. 架构方案反思
    reflect_task_shrimp-video-factory "架构方案批判性审查：技术选择合理性、模块设计优化、潜在风险识别"
    
    # 2. 方案验证
    verify_task_shrimp-video-factory [架构设计任务ID]
    
    # 3. 文档完善和交接
    update_task_shrimp-video-factory [主任务ID] --summary "架构设计完成，生成02-architecture.md，为Jarvis建立开发依赖"
    ```
    
    #### 验证检查清单
    - [ ] 架构满足所有功能需求
    - [ ] 非功能需求得到充分考虑
    - [ ] 技术选择有充分理由支撑
    - [ ] 模块划分清晰且低耦合
    - [ ] 接口定义完整且一致
    - [ ] 部署方案可行且经济
    - [ ] 文档完整且易于理解
    - [ ] 为开发阶段提供清晰指导
  </process>

  <criteria>
    ## 架构设计质量标准
    
    ### 文档完整性 (25%)
    - ✅ 严格遵循design.md标准模板
    - ✅ 包含完整的架构图和组件图
    - ✅ 技术选择有明确理由说明
    - ✅ 接口定义清晰且一致
    
    ### 技术合理性 (30%)
    - ✅ 技术选择适合项目规模和约束
    - ✅ 考虑了长期维护和扩展性
    - ✅ 技术栈之间兼容性良好
    - ✅ 有充分的技术调研支撑
    
    ### 架构质量 (25%)
    - ✅ 模块划分清晰且职责单一
    - ✅ 组件间耦合度低，内聚度高
    - ✅ 支持测试和监控
    - ✅ 架构演进路径清晰
    
    ### 实施可行性 (20%)
    - ✅ 考虑了一人公司的资源限制
    - ✅ 开发复杂度在可控范围内
    - ✅ 部署和运维方案可行
    - ✅ 为开发阶段提供清晰指导
    
    ### 验收标准
    - **文档质量**：02-architecture.md文档完整且符合标准模板
    - **技术决策**：所有重要技术选择都有充分的调研和理由
    - **架构图表**：提供清晰的系统架构图和组件关系图
    - **开发指导**：为Jarvis角色提供明确的技术实现路径
    - **任务验证**：通过verify_task_shrimp-video-factory验证，得分≥80分
  </criteria>
</execution>
