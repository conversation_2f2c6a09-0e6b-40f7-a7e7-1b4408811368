<execution>
  <constraint>
    ## 技术选型客观限制
    - **一人公司约束**：技术栈必须考虑单人学习、开发、维护的可行性
    - **资源限制**：服务器成本、开发工具成本、第三方服务成本的控制
    - **时间约束**：学习新技术的时间成本不能超过项目时间预算
    - **兼容性约束**：新技术必须与现有开发环境和工具链兼容
  </constraint>

  <rule>
    ## 技术选型强制规则
    - **调研先行**：重要技术选择必须通过research_mode_shrimp-video-factory进行充分调研
    - **理由文档化**：每个技术选择都必须有明确的理由和权衡分析
    - **风险评估强制**：必须识别和评估技术选择的主要风险
    - **备选方案**：关键技术必须有备选方案，避免单点依赖
    - **原型验证**：对于不确定的技术选择，必须通过原型验证可行性
  </rule>

  <guideline>
    ## 技术选型指导原则
    - **成熟度优先**：优先选择成熟稳定的技术，避免过于前沿的技术
    - **生态考虑**：重视技术的生态系统和社区支持
    - **学习曲线**：考虑技术的学习难度和上手时间
    - **长期维护**：选择有长期支持和活跃维护的技术
    - **成本效益**：平衡技术带来的收益和投入的成本
  </guideline>

  <process>
    ## 技术选型标准流程
    
    ### 阶段1：技术需求分析
    ```bash
    # 1. 分析技术需求
    analyze_task_shrimp-video-factory "技术需求分析：基于功能需求和非功能需求确定技术要求"
    
    # 2. 识别技术约束
    process_thought_shrimp-video-factory "技术约束识别：一人公司资源限制、现有技术栈、时间预算"
    ```
    
    #### 需求分析矩阵
    | 需求类型 | 具体要求 | 技术影响 | 优先级 |
    |----------|----------|----------|--------|
    | 功能需求 | 用户认证、数据处理、API接口 | 后端框架选择 | 高 |
    | 性能需求 | 响应时间、并发量、数据量 | 技术栈性能特征 | 中 |
    | 扩展需求 | 用户增长、功能扩展 | 架构可扩展性 | 中 |
    | 维护需求 | 代码可读性、调试便利性 | 开发工具生态 | 高 |
    
    ### 阶段2：技术调研和评估
    ```bash
    # 1. 前端技术栈调研
    research_mode_shrimp-video-factory "前端框架对比：React vs Vue vs Angular 在一人公司场景下的适用性"
    
    # 2. 后端技术栈调研
    research_mode_shrimp-video-factory "后端技术选型：Node.js vs Python vs Go 性能和开发效率对比"
    
    # 3. 数据库技术调研
    research_mode_shrimp-video-factory "数据库选择：PostgreSQL vs MongoDB vs SQLite 在小型项目中的优劣"
    
    # 4. 部署方案调研
    research_mode_shrimp-video-factory "部署方案比较：Docker容器化 vs 传统VPS vs 云服务 成本效益分析"
    ```
    
    #### 技术评估维度
    ```mermaid
    mindmap
      root((技术评估))
        成熟度
          版本稳定性
          社区活跃度
          企业采用度
          文档完整性
        学习成本
          上手难度
          学习资源
          开发工具
          调试便利性
        性能表现
          运行性能
          开发效率
          扩展能力
          资源消耗
        生态支持
          第三方库
          工具链
          插件系统
          社区支持
        维护成本
          更新频率
          向后兼容
          bug修复
          长期支持
    ```
    
    ### 阶段3：技术选择和决策
    ```bash
    # 1. 创建技术选型决策任务
    plan_task_shrimp-video-factory "技术选型决策：[项目名称] - 基于调研结果进行技术栈最终选择"
    split_tasks_shrimp-video-factory [技术选型决策任务ID]
    
    # 2. 执行技术选择
    execute_task_shrimp-video-factory [前端技术选择任务ID]
    execute_task_shrimp-video-factory [后端技术选择任务ID]
    execute_task_shrimp-video-factory [数据库选择任务ID]
    execute_task_shrimp-video-factory [部署方案选择任务ID]
    ```
    
    #### 决策矩阵模板
    ```markdown
    ## 技术选型决策矩阵
    
    ### 前端框架选择
    | 框架 | 成熟度 | 学习成本 | 性能 | 生态 | 维护性 | 总分 |
    |------|--------|----------|------|------|--------|------|
    | React | 9 | 7 | 8 | 9 | 8 | 8.2 |
    | Vue | 8 | 9 | 8 | 7 | 8 | 8.0 |
    | Angular | 9 | 5 | 9 | 8 | 9 | 8.0 |
    
    **选择结果**: React
    **选择理由**: 生态最成熟，社区支持最好，虽然学习成本略高但长期收益更大
    ```
    
    ### 阶段4：风险评估和缓解
    ```bash
    # 1. 技术风险评估
    reflect_task_shrimp-video-factory "技术选型风险分析：识别技术选择的潜在风险和缓解措施"
    
    # 2. 备选方案制定
    process_thought_shrimp-video-factory "备选技术方案：为关键技术选择制定备选方案和切换策略"
    ```
    
    #### 风险评估模板
    ```markdown
    ## 技术风险评估
    
    ### 高风险项
    - **新技术采用风险**: 选择过新的技术可能存在稳定性问题
    - **学习成本风险**: 技术栈过于复杂可能影响开发进度
    - **供应商锁定风险**: 过度依赖特定云服务可能增加迁移成本
    
    ### 缓解措施
    - **技术原型验证**: 对关键技术进行原型验证
    - **渐进式采用**: 分阶段引入新技术，降低风险
    - **备选方案准备**: 为关键技术准备备选方案
    ```
    
    ### 阶段5：技术选型文档化
    ```bash
    # 1. 更新架构文档
    update_task_shrimp-video-factory [技术选型任务ID] --summary "技术选型完成，更新Technology Stack部分"
    
    # 2. 验证技术选型
    verify_task_shrimp-video-factory [技术选型任务ID]
    ```
  </process>

  <criteria>
    ## 技术选型质量标准
    
    ### 调研充分性 (30%)
    - ✅ 对主要技术选项进行了充分调研
    - ✅ 调研覆盖了技术的主要评估维度
    - ✅ 收集了足够的技术对比数据
    - ✅ 参考了相关的最佳实践案例
    
    ### 决策合理性 (35%)
    - ✅ 技术选择符合项目需求和约束
    - ✅ 考虑了一人公司的资源限制
    - ✅ 平衡了短期效率和长期维护
    - ✅ 技术栈之间具有良好兼容性
    
    ### 风险控制 (20%)
    - ✅ 识别了主要的技术风险
    - ✅ 制定了相应的缓解措施
    - ✅ 准备了关键技术的备选方案
    - ✅ 评估了技术选择的长期影响
    
    ### 文档质量 (15%)
    - ✅ 技术选择理由清晰明确
    - ✅ 提供了详细的技术对比分析
    - ✅ 包含了完整的技术栈清单
    - ✅ 为开发阶段提供了技术指导
    
    ### 验收标准
    - **调研报告**：每个主要技术选择都有详细的调研报告
    - **决策文档**：技术选择有明确的理由和权衡分析
    - **风险评估**：识别了主要风险并制定了缓解措施
    - **技术栈清单**：提供了完整的技术栈和版本要求
    - **开发指导**：为开发阶段提供了技术使用指导
  </criteria>
</execution>
