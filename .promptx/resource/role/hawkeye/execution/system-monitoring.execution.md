<execution>
  <constraint>
    ## 系统监控的客观限制
    - **资源消耗约束**：监控系统本身不能过度消耗系统资源
    - **数据存储限制**：监控数据的存储容量和保留期限有限
    - **网络带宽约束**：监控数据传输不能影响业务网络性能
    - **隐私合规约束**：监控数据收集必须符合隐私保护要求
    - **成本控制约束**：监控工具和服务的成本必须在预算范围内
  </constraint>

  <rule>
    ## 系统监控的强制性规则
    - **全覆盖监控**：关键业务功能和系统组件必须100%覆盖监控
    - **实时告警**：关键指标异常必须在5分钟内触发告警
    - **数据完整性**：监控数据必须完整、准确、可追溯
    - **告警分级**：必须建立明确的告警分级和响应机制
    - **监控自监控**：监控系统本身必须被监控，确保监控可靠性
  </rule>

  <guideline>
    ## 系统监控的指导原则
    - **业务导向**：监控指标必须与业务目标和用户体验直接相关
    - **预防为主**：重点监控可能导致问题的先行指标
    - **分层监控**：建立基础设施、应用、业务的分层监控体系
    - **智能告警**：使用智能算法减少误报，提高告警质量
    - **可视化优先**：提供直观的监控仪表板和数据可视化
  </guideline>

  <process>
    ## 系统监控的标准化执行步骤
    
    ### Phase 1: 监控体系设计与规划 (60分钟)
    
    ```mermaid
    graph TD
        A[监控需求分析] --> B[监控指标设计]
        B --> C[监控架构规划]
        C --> D[告警策略制定]
        D --> E[监控工具选型]
        E --> F[实施计划制定]
        
        B --> B1[业务指标]
        B --> B2[技术指标]
        B --> B3[用户体验指标]
        
        C --> C1[数据收集层]
        C --> C2[数据处理层]
        C --> C3[数据展示层]
        
        D --> D1[告警阈值]
        D --> D2[告警分级]
        D --> D3[响应流程]
    ```
    
    **关键活动**：
    1. **指标体系设计**：设计覆盖业务、技术、用户体验的完整指标体系
    2. **架构规划**：规划监控数据的收集、存储、处理、展示架构
    3. **告警策略**：制定智能化的告警策略，平衡敏感性和准确性
    
    ### Phase 2: 监控工具部署与配置 (90分钟)
    
    ```mermaid
    flowchart LR
        A[监控工具安装] --> B[数据源配置]
        B --> C[指标采集配置]
        C --> D[告警规则配置]
        D --> E[仪表板创建]
        E --> F[权限和安全配置]
    ```
    
    **关键活动**：
    1. **工具部署**：部署和配置监控工具，确保稳定运行
    2. **数据采集**：配置各种数据源的监控数据采集
    3. **仪表板设计**：创建直观的监控仪表板和报表
    4. **安全配置**：配置监控系统的访问权限和数据安全
    
    ### Phase 3: 监控运行与维护 (持续进行)
    
    ```mermaid
    flowchart TD
        A[监控系统运行] --> B[实时数据监控]
        B --> C{异常检测}
        C -->|正常| D[数据记录存储]
        C -->|异常| E[告警触发]
        E --> F[问题分析诊断]
        F --> G[响应措施执行]
        G --> H[处理结果记录]
        D --> I[定期报告生成]
        H --> I
        I --> J[监控优化调整]
    ```
    
    **关键活动**：
    1. **实时监控**：7×24小时实时监控系统状态和性能指标
    2. **异常处理**：快速响应和处理监控告警，解决系统问题
    3. **数据分析**：定期分析监控数据，识别趋势和改进机会
    4. **系统优化**：基于监控数据持续优化监控体系和告警策略
    
    ### Phase 4: 监控效果评估与改进 (每月进行)
    
    ```mermaid
    graph LR
        A[监控效果评估] --> B[指标有效性分析]
        B --> C[告警质量评估]
        C --> D[响应效率分析]
        D --> E[用户满意度调查]
        E --> F[改进计划制定]
        F --> G[监控体系优化]
    ```
    
    **关键活动**：
    1. **效果评估**：评估监控体系的有效性和准确性
    2. **质量分析**：分析告警的准确性、及时性、完整性
    3. **持续改进**：基于评估结果持续改进监控体系
    
    ### 监控指标体系框架
    
    ```mermaid
    mindmap
      root((监控指标体系))
        基础设施监控
          服务器资源
            CPU使用率
            内存使用率
            磁盘空间
            网络流量
          数据库性能
            连接数
            查询响应时间
            锁等待时间
            缓存命中率
        应用监控
          应用性能
            响应时间
            吞吐量
            错误率
            可用性
          业务指标
            用户活跃度
            功能使用率
            转化率
            业务流程完成率
        用户体验监控
          前端性能
            页面加载时间
            交互响应时间
            资源加载失败率
          用户行为
            用户路径分析
            功能使用统计
            错误反馈
            满意度调查
    ```
    
    ### 告警管理流程
    
    ```mermaid
    flowchart TD
        A[告警触发] --> B{告警级别}
        B -->|P1-紧急| C[立即通知]
        B -->|P2-重要| D[5分钟内通知]
        B -->|P3-一般| E[30分钟内通知]
        B -->|P4-信息| F[日报汇总]
        
        C --> G[立即响应]
        D --> H[快速响应]
        E --> I[计划响应]
        F --> J[定期处理]
        
        G --> K[问题解决]
        H --> K
        I --> K
        J --> K
        
        K --> L[告警关闭]
        L --> M[处理记录]
        M --> N[经验总结]
    ```
  </process>

  <criteria>
    ## 系统监控的评价标准
    
    ### 监控覆盖度
    - ✅ 关键业务功能监控覆盖率 = 100%
    - ✅ 系统组件监控覆盖率 ≥ 95%
    - ✅ 用户体验监控覆盖率 ≥ 90%
    - ✅ 监控数据完整性 ≥ 99%
    
    ### 告警质量
    - ✅ 告警准确率 ≥ 90%（减少误报）
    - ✅ 告警及时性 ≤ 5分钟（关键告警）
    - ✅ 告警完整性 ≥ 95%（减少漏报）
    - ✅ 告警响应时间 ≤ 15分钟
    
    ### 系统性能
    - ✅ 监控系统可用性 ≥ 99.9%
    - ✅ 监控数据延迟 ≤ 30秒
    - ✅ 监控系统资源消耗 ≤ 5%
    - ✅ 数据存储效率 ≥ 80%
    
    ### 业务价值
    - ✅ 问题发现时间缩短 ≥ 50%
    - ✅ 系统故障恢复时间缩短 ≥ 40%
    - ✅ 用户满意度提升 ≥ 10%
    - ✅ 运维效率提升 ≥ 30%
  </criteria>
</execution>
