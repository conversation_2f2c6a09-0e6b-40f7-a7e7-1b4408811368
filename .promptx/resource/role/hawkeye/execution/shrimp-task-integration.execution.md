<execution>
  <constraint>
    ## Shrimp任务管理集成的客观限制
    - **任务系统依赖**：必须依赖shrimp-video-factory任务管理系统的完整功能
    - **数据一致性约束**：维护任务数据必须与项目整体任务数据保持一致
    - **权限控制约束**：任务操作必须在授权范围内进行
    - **性能影响约束**：任务管理操作不能影响系统正常运行
    - **历史数据约束**：必须保持任务历史数据的完整性和可追溯性
  </constraint>

  <rule>
    ## Shrimp任务管理集成的强制性规则
    - **全流程回顾强制**：维护工作开始前必须完整回顾所有历史任务
    - **任务分解标准**：维护任务必须分解为1-2小时可完成的原子任务
    - **质量门禁强制**：所有维护任务必须通过verify_task验证≥80分
    - **文档同步强制**：任务完成必须同步更新相关文档和记录
    - **依赖关系维护**：必须正确维护与其他角色任务的依赖关系
  </rule>

  <guideline>
    ## Shrimp任务管理集成的指导原则
    - **数据驱动决策**：基于任务历史数据进行维护决策和规划
    - **经验传承优先**：将维护经验转化为可复用的任务模板和知识
    - **协作效率优化**：通过任务系统优化四角色协作效率
    - **质量持续改进**：基于任务执行质量数据持续改进维护流程
    - **知识系统化**：将维护知识系统化存储在任务系统中
  </guideline>

  <process>
    ## Shrimp任务管理集成的标准化执行步骤
    
    ### Phase 1: 项目全面回顾与分析 (45分钟)
    
    ```mermaid
    flowchart TD
        A[启动维护工作] --> B[查看项目任务全貌]
        B --> C[分析需求实现情况]
        C --> D[评估架构决策效果]
        D --> E[检查开发质量]
        E --> F[识别维护需求]
        
        B --> B1[list_tasks_shrimp-video-factory all]
        C --> C1[query_task_shrimp-video-factory 需求分析]
        D --> D1[query_task_shrimp-video-factory 架构设计]
        E --> E1[query_task_shrimp-video-factory 代码实现]
        F --> F1[process_thought_shrimp-video-factory 项目健康度分析]
    ```
    
    **核心工具使用**：
    ```bash
    # 1. 全面回顾项目任务历史
    list_tasks_shrimp-video-factory all
    
    # 2. 分析各阶段任务完成情况
    query_task_shrimp-video-factory "Banner 需求分析"
    query_task_shrimp-video-factory "Stark 架构设计"  
    query_task_shrimp-video-factory "Jarvis 代码实现"
    
    # 3. 深度分析项目健康度
    process_thought_shrimp-video-factory "基于任务历史的项目健康度分析"
    process_thought_shrimp-video-factory "技术债务和改进机会识别"
    ```
    
    ### Phase 2: 维护任务规划与分解 (30分钟)
    
    ```mermaid
    flowchart LR
        A[维护需求确认] --> B[创建维护主任务]
        B --> C[任务分解规划]
        C --> D[依赖关系建立]
        D --> E[优先级排序]
        E --> F[资源分配]
        
        B --> B1[plan_task_shrimp-video-factory]
        C --> C1[split_tasks_shrimp-video-factory]
        D --> D1[update_task_shrimp-video-factory]
    ```
    
    **任务规划工具使用**：
    ```bash
    # 1. 创建维护主任务
    plan_task_shrimp-video-factory "项目维护：[项目名称] - 文档更新、性能优化、持续改进"
    
    # 2. 分解维护任务
    split_tasks_shrimp-video-factory [维护主任务ID]
    # 分解为：
    # - 文档同步维护任务
    # - 性能监控优化任务  
    # - 用户反馈处理任务
    # - 技术债务清理任务
    # - 系统安全检查任务
    
    # 3. 建立任务依赖关系
    update_task_shrimp-video-factory [任务ID] --dependencies [依赖任务列表]
    ```
    
    ### Phase 3: 维护任务执行与监控 (120-180分钟)
    
    ```mermaid
    flowchart TD
        A[开始维护执行] --> B[执行文档维护]
        B --> C[执行性能优化]
        C --> D[处理用户反馈]
        D --> E[清理技术债务]
        E --> F[系统安全检查]
        
        B --> B1[execute_task_shrimp-video-factory 文档维护]
        C --> C1[execute_task_shrimp-video-factory 性能优化]
        D --> D1[execute_task_shrimp-video-factory 用户反馈]
        E --> E1[execute_task_shrimp-video-factory 技术债务]
        F --> F1[execute_task_shrimp-video-factory 安全检查]
        
        F --> G[维护质量验证]
        G --> G1[verify_task_shrimp-video-factory]
    ```
    
    **维护执行工具使用**：
    ```bash
    # 1. 按优先级执行维护任务
    execute_task_shrimp-video-factory [文档维护任务ID]
    execute_task_shrimp-video-factory [性能优化任务ID]
    execute_task_shrimp-video-factory [用户反馈处理任务ID]
    execute_task_shrimp-video-factory [技术债务清理任务ID]
    
    # 2. 深度思考复杂维护问题
    process_thought_shrimp-video-factory "系统长期发展规划思考"
    process_thought_shrimp-video-factory "维护流程优化建议"
    
    # 3. 验证维护任务质量
    verify_task_shrimp-video-factory [维护任务ID] --score ≥80
    ```
    
    ### Phase 4: 知识沉淀与经验传承 (60分钟)
    
    ```mermaid
    flowchart LR
        A[维护完成] --> B[经验总结分析]
        B --> C[最佳实践提取]
        C --> D[流程改进建议]
        D --> E[知识库更新]
        E --> F[团队能力提升]
        
        B --> B1[research_mode_shrimp-video-factory]
        C --> C1[process_thought_shrimp-video-factory]
        E --> E1[update_task_shrimp-video-factory]
    ```
    
    **知识管理工具使用**：
    ```bash
    # 1. 研究维护最佳实践
    research_mode_shrimp-video-factory "项目维护最佳实践调研"
    research_mode_shrimp-video-factory "技术栈升级策略研究"
    
    # 2. 深度思考改进机会
    process_thought_shrimp-video-factory "基于维护经验的流程优化建议"
    process_thought_shrimp-video-factory "四角色协作效率提升方案"
    
    # 3. 更新任务和文档
    update_task_shrimp-video-factory [主任务ID] --summary "维护完成，生成04-maintenance.md"
    update_task_shrimp-video-factory [主任务ID] --status completed
    ```
    
    ### 跨角色协作机制
    
    ```mermaid
    graph TD
        A[Hawkeye维护者] --> B[Banner分析师]
        A --> C[Stark架构师]
        A --> D[Jarvis开发者]
        
        B --> B1[需求实现效果反馈]
        B --> B2[用户需求变化分析]
        
        C --> C1[架构决策效果评估]
        C --> C2[技术选型优化建议]
        
        D --> D1[代码质量评估反馈]
        D --> D2[开发流程改进建议]
        
        A --> E[项目整体健康度报告]
        E --> F[四角色协作优化建议]
    ```
    
    ### 维护质量控制流程
    
    ```mermaid
    flowchart TD
        A[维护任务开始] --> B{任务复杂度评估}
        B -->|简单| C[标准维护流程]
        B -->|复杂| D[深度分析流程]
        
        C --> E[执行维护操作]
        D --> F[process_thought深度思考]
        F --> E
        
        E --> G[质量验证检查]
        G --> H{verify_task评分}
        H -->|≥80分| I[任务完成]
        H -->|<80分| J[问题分析改进]
        J --> E
        
        I --> K[经验记录沉淀]
        K --> L[知识库更新]
    ```
  </process>

  <criteria>
    ## Shrimp任务管理集成的评价标准
    
    ### 任务管理效率
    - ✅ 任务回顾完成时间 ≤ 45分钟
    - ✅ 任务分解准确率 ≥ 95%
    - ✅ 任务执行成功率 ≥ 90%
    - ✅ 任务质量验证通过率 ≥ 85%
    
    ### 数据利用效果
    - ✅ 历史任务数据利用率 ≥ 80%
    - ✅ 维护决策数据支撑率 ≥ 90%
    - ✅ 问题预测准确率 ≥ 70%
    - ✅ 改进建议采纳率 ≥ 60%
    
    ### 协作质量
    - ✅ 跨角色反馈及时性 ≤ 24小时
    - ✅ 协作流程优化效果 ≥ 20%
    - ✅ 团队满意度 ≥ 85%
    - ✅ 知识传承完整性 ≥ 90%
    
    ### 长期价值
    - ✅ 维护效率提升 ≥ 30%
    - ✅ 系统稳定性改善 ≥ 25%
    - ✅ 技术债务减少 ≥ 40%
    - ✅ 团队能力提升 ≥ 15%
  </criteria>
</execution>
