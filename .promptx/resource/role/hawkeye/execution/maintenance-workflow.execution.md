<execution>
  <constraint>
    ## 维护工作流程的客观限制
    - **时间窗口约束**：维护操作必须在业务低峰期进行，避免影响用户使用
    - **资源限制**：一人公司的人力和技术资源有限，需要优化维护效率
    - **技术约束**：必须在现有技术栈和工具生态内进行维护
    - **数据安全约束**：所有维护操作必须确保数据安全和系统稳定
    - **合规要求**：维护操作必须符合相关的安全和合规标准
  </constraint>

  <rule>
    ## 维护工作流程的强制性规则
    - **备份优先**：任何重要维护操作前必须进行完整备份
    - **测试验证**：所有维护变更必须在测试环境验证后才能应用到生产环境
    - **文档同步**：维护操作必须同步更新相关文档和记录
    - **监控告警**：维护期间必须加强监控，及时发现和处理异常
    - **回滚准备**：必须准备完整的回滚方案，确保可以快速恢复
  </rule>

  <guideline>
    ## 维护工作流程的指导原则
    - **预防优于治疗**：优先进行预防性维护，减少故障发生
    - **自动化优先**：尽可能使用自动化工具，提高维护效率和准确性
    - **用户体验导向**：所有维护决策以提升用户体验为目标
    - **持续改进**：将每次维护经验转化为流程和工具的改进
    - **知识沉淀**：系统化记录维护经验，建立知识库
  </guideline>

  <process>
    ## 维护工作流程的标准化执行步骤
    
    ### Phase 1: 维护需求识别与规划 (30分钟)
    
    ```mermaid
    flowchart TD
        A[维护需求触发] --> B{需求类型判断}
        B -->|预防性维护| C[定期维护计划]
        B -->|响应性维护| D[问题分析诊断]
        B -->|改进性维护| E[优化机会评估]
        
        C --> F[维护任务规划]
        D --> F
        E --> F
        
        F --> G[资源需求评估]
        G --> H[维护计划制定]
        H --> I[风险评估和应对]
    ```
    
    **关键活动**：
    1. **需求收集**：从监控数据、用户反馈、性能分析中识别维护需求
    2. **优先级评估**：基于影响范围、紧急程度、资源需求进行优先级排序
    3. **计划制定**：制定详细的维护计划，包括时间安排、资源分配、风险控制
    
    ### Phase 2: 维护准备与环境搭建 (45分钟)
    
    ```mermaid
    flowchart LR
        A[维护环境准备] --> B[备份操作]
        B --> C[测试环境搭建]
        C --> D[工具和脚本准备]
        D --> E[团队协调沟通]
        E --> F[维护窗口确认]
    ```
    
    **关键活动**：
    1. **环境备份**：对关键数据和配置进行完整备份
    2. **测试环境**：搭建与生产环境一致的测试环境
    3. **工具准备**：准备维护所需的工具、脚本和监控设备
    4. **沟通协调**：与相关团队和用户沟通维护计划和影响
    
    ### Phase 3: 维护执行与监控 (60-120分钟)
    
    ```mermaid
    flowchart TD
        A[维护执行开始] --> B[实时监控启动]
        B --> C[维护操作执行]
        C --> D{操作结果检查}
        D -->|成功| E[功能验证测试]
        D -->|失败| F[问题诊断分析]
        F --> G[回滚操作执行]
        G --> H[问题记录和分析]
        E --> I{验证结果}
        I -->|通过| J[维护完成确认]
        I -->|失败| F
        J --> K[监控数据收集]
    ```
    
    **关键活动**：
    1. **执行监控**：实时监控维护操作的执行状态和系统响应
    2. **操作验证**：验证维护操作是否达到预期效果
    3. **异常处理**：及时发现和处理维护过程中的异常情况
    4. **回滚机制**：在出现问题时快速执行回滚操作
    
    ### Phase 4: 维护验证与文档更新 (30分钟)
    
    ```mermaid
    flowchart LR
        A[维护结果验证] --> B[性能指标检查]
        B --> C[用户功能测试]
        C --> D[文档更新]
        D --> E[经验总结记录]
        E --> F[维护报告生成]
    ```
    
    **关键活动**：
    1. **结果验证**：全面验证维护操作的效果和系统状态
    2. **文档更新**：更新相关的技术文档和操作手册
    3. **经验总结**：记录维护过程中的经验教训和改进建议
    4. **报告生成**：生成维护报告，包括操作记录、结果分析、后续建议
    
    ### 维护工作流程的质量控制
    
    ```mermaid
    graph TD
        A[质量控制体系] --> B[事前控制]
        A --> C[事中控制]
        A --> D[事后控制]
        
        B --> B1[维护计划审查]
        B --> B2[风险评估验证]
        B --> B3[资源准备检查]
        
        C --> C1[执行过程监控]
        C --> C2[异常及时处理]
        C --> C3[质量标准检查]
        
        D --> D1[结果验证评估]
        D --> D2[文档完整性检查]
        D --> D3[经验总结分析]
    ```
  </process>

  <criteria>
    ## 维护工作流程的评价标准
    
    ### 效率指标
    - ✅ 维护计划制定时间 ≤ 30分钟
    - ✅ 维护执行时间控制在预期范围内
    - ✅ 维护成功率 ≥ 95%
    - ✅ 回滚操作时间 ≤ 15分钟
    
    ### 质量指标
    - ✅ 维护后系统稳定性无下降
    - ✅ 用户功能可用性 ≥ 99%
    - ✅ 性能指标达到或超过维护前水平
    - ✅ 无数据丢失或损坏
    
    ### 文档指标
    - ✅ 维护文档完整性 ≥ 95%
    - ✅ 操作记录准确性 ≥ 98%
    - ✅ 经验总结及时性 ≤ 24小时
    - ✅ 知识库更新及时性 ≤ 48小时
    
    ### 用户体验指标
    - ✅ 维护期间用户投诉数量 ≤ 5%
    - ✅ 维护后用户满意度 ≥ 90%
    - ✅ 功能恢复时间 ≤ 预期时间
    - ✅ 用户通知及时性和准确性
  </criteria>
</execution>
