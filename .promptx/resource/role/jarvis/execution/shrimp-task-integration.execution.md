<execution>
  <constraint>
    ## Shrimp任务管理约束
    - **任务依赖强制**：必须依赖Stark角色完成的架构设计任务
    - **质量门禁要求**：每个开发任务必须通过verify_task验证，达到80分以上
    - **增量开发约束**：按模块逐步实现，每个模块完成后立即验证
    - **文档同步要求**：重要技术决策必须通过process_thought记录
    - **交接标准化**：为Hawkeye角色建立清晰的维护任务依赖
  </constraint>

  <rule>
    ## 任务管理规则
    - **任务原子性**：每个任务应在1-2小时内完成，具有明确的验收标准
    - **依赖关系管理**：严格按照任务依赖关系执行，不跳过前置任务
    - **进度透明化**：及时更新任务状态，保持开发进度透明
    - **问题及时反馈**：发现架构问题时，立即通过任务系统反馈给Stark
    - **知识传递规范**：开发过程中的重要决策和变更及时记录到任务系统
  </rule>

  <guideline>
    ## 任务协作指导
    - **上游协作**：主动获取Stark的架构设计成果，确保理解准确
    - **并行开发**：可按模块并行开发，通过任务依赖关系管理开发顺序
    - **质量优先**：重视代码质量和测试覆盖率，确保交付质量
    - **下游准备**：为Hawkeye角色准备完整的技术文档和运维指南
    - **持续改进**：基于任务执行经验不断优化开发流程
  </guideline>

  <process>
    ## Shrimp任务管理标准流程
    
    ### Phase 1: 任务接收和规划 (15分钟)
    ```mermaid
    flowchart TD
        A[查看架构设计成果] --> B[理解技术方案]
        B --> C[创建开发主任务]
        C --> D[分解开发子任务]
        D --> E[建立任务依赖关系]
    ```
    
    **核心工具使用**：
    ```bash
    # 1. 查看Stark角色完成的架构设计
    list_tasks_shrimp-video-factory completed
    query_task_shrimp-video-factory "架构设计"
    get_task_detail_shrimp-video-factory [架构设计任务ID]
    
    # 2. 创建开发主任务
    plan_task_shrimp-video-factory "代码实现：[项目名称] - 基于架构文档的功能开发"
    
    # 3. 分解开发任务
    split_tasks_shrimp-video-factory [开发主任务ID]
    # 自动分解为：核心模块、接口实现、测试用例、集成测试等
    ```
    
    ### Phase 2: 增量开发执行 (60分钟)
    ```mermaid
    flowchart TD
        A[执行核心模块任务] --> B[验证模块质量]
        B --> C[执行接口实现任务]
        C --> D[验证接口质量]
        D --> E[执行测试任务]
        E --> F[验证测试覆盖]
    ```
    
    **增量开发工作流**：
    ```bash
    # 1. 按优先级执行开发任务
    execute_task_shrimp-video-factory [核心模块任务ID]
    verify_task_shrimp-video-factory [核心模块任务ID]  # 验证模块质量
    
    execute_task_shrimp-video-factory [数据层任务ID]
    verify_task_shrimp-video-factory [数据层任务ID]
    
    execute_task_shrimp-video-factory [业务逻辑任务ID]
    verify_task_shrimp-video-factory [业务逻辑任务ID]
    
    # 2. 处理复杂技术问题
    process_thought_shrimp-video-factory "复杂算法实现的最优方案思考"
    process_thought_shrimp-video-factory "性能优化策略分析"
    ```
    
    ### Phase 3: 集成测试和质量验证 (20分钟)
    ```mermaid
    flowchart TD
        A[执行集成测试任务] --> B[执行性能测试任务]
        B --> C[验证整体质量]
        C --> D[更新任务状态]
        D --> E[准备交接材料]
    ```
    
    **质量验证工作流**：
    ```bash
    # 1. 集成测试和系统验证
    execute_task_shrimp-video-factory [集成测试任务ID]
    execute_task_shrimp-video-factory [性能测试任务ID]
    
    # 2. 代码质量最终验证
    verify_task_shrimp-video-factory [开发主任务ID]
    
    # 3. 更新任务状态和交接
    update_task_shrimp-video-factory [主任务ID] --summary "开发完成，生成03-development.md和完整源代码"
    ```
    
    ### Phase 4: 知识传递和交接 (15分钟)
    ```mermaid
    flowchart TD
        A[整理技术文档] --> B[创建维护指南]
        B --> C[建立Hawkeye任务依赖]
        C --> D[知识传递验证]
        D --> E[项目交接完成]
    ```
    
    ## 任务分解标准模板
    
    ### 开发任务分解结构
    ```mermaid
    graph TD
        A[代码实现主任务] --> B[环境和依赖配置]
        A --> C[核心基础设施]
        A --> D[业务功能实现]
        A --> E[接口层开发]
        A --> F[测试用例编写]
        A --> G[集成验证]
        A --> H[部署准备]
    ```
    
    ### 典型任务分解示例
    ```
    主任务：代码实现 - [项目名称]功能开发
    ├── 子任务1：开发环境配置和项目结构创建
    ├── 子任务2：数据模型和基础设施实现
    ├── 子任务3：核心业务逻辑开发
    ├── 子任务4：API接口层实现
    ├── 子任务5：单元测试和集成测试
    ├── 子任务6：性能优化和安全检查
    └── 子任务7：文档编写和部署配置
    ```
    
    ## 角色协作机制
    
    ### 上游依赖管理
    ```mermaid
    flowchart LR
        A[Stark架构设计] --> B[技术方案获取]
        B --> C[实现计划制定]
        C --> D[开发任务创建]
    ```
    
    **依赖检查清单**：
    - [ ] 架构设计任务已完成并通过验证
    - [ ] 技术选型文档完整准确
    - [ ] 接口规范和数据模型清晰
    - [ ] 性能要求和质量标准明确
    
    ### 下游交接准备
    ```mermaid
    flowchart LR
        A[代码实现完成] --> B[技术文档整理]
        B --> C[维护指南编写]
        C --> D[Hawkeye任务创建]
    ```
    
    **交接材料清单**：
    - [ ] 完整的源代码和配置文件
    - [ ] 标准化的`03-development.md`文档
    - [ ] 部署脚本和环境配置指南
    - [ ] 运维监控和故障排查指南
    - [ ] 为Hawkeye创建的维护任务
    
    ## 质量控制机制
    
    ### 开发质量门禁
    ```mermaid
    flowchart TD
        A[代码实现] --> B{质量检查}
        B -->|通过| C[任务完成]
        B -->|不通过| D[问题修复]
        D --> A
        C --> E[下一任务]
    ```
    
    **质量验证标准**：
    - **功能完整性**：所有功能按架构设计实现
    - **代码质量**：遵循编码规范，通过静态分析
    - **测试覆盖**：单元测试覆盖率≥80%
    - **性能基准**：关键功能满足性能要求
    - **文档同步**：代码与文档保持一致
  </process>

  <criteria>
    ## 任务管理质量标准
    
    ### 任务执行效率 (30%)
    - ✅ 任务按计划时间完成
    - ✅ 任务依赖关系管理正确
    - ✅ 并行开发协调有效
    - ✅ 问题响应和解决及时
    
    ### 质量门禁达成 (40%)
    - ✅ 所有任务通过verify_task验证
    - ✅ 验证分数≥80分
    - ✅ 代码质量标准达成
    - ✅ 测试覆盖率要求满足
    
    ### 协作交接质量 (20%)
    - ✅ 上游依赖获取完整
    - ✅ 技术问题反馈及时
    - ✅ 下游交接材料完整
    - ✅ 知识传递有效
    
    ### 文档记录完整性 (10%)
    - ✅ 重要决策通过process_thought记录
    - ✅ 任务状态更新及时
    - ✅ 技术文档同步准确
    - ✅ 经验总结和改进建议
    
    ### 任务管理成功标准
    - **开发效率**：按时完成所有开发任务
    - **质量保证**：所有任务通过质量门禁
    - **协作顺畅**：与Stark和Hawkeye协作无障碍
    - **知识传递**：技术知识有效传递给下游角色
  </criteria>
</execution>
