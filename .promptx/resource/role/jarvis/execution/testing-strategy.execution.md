<execution>
  <constraint>
    ## 测试质量约束
    - **覆盖率硬要求**：单元测试覆盖率必须≥80%，关键业务逻辑100%覆盖
    - **测试类型完整性**：必须包含单元测试、集成测试、功能测试、性能测试
    - **自动化要求**：所有测试必须可以自动化执行，支持持续集成
    - **测试数据管理**：测试数据必须独立管理，不影响生产数据
    - **测试环境隔离**：测试环境必须与开发和生产环境隔离
  </constraint>

  <rule>
    ## 测试实施规则
    - **测试驱动开发**：先编写测试用例，再实现功能代码
    - **测试金字塔原则**：单元测试为基础，集成测试为补充，端到端测试为验证
    - **失败快速原则**：测试失败时立即停止，快速定位问题
    - **测试独立性**：每个测试用例必须独立，不依赖其他测试的执行结果
    - **测试可重复性**：测试结果必须可重复，不受执行环境和时间影响
  </rule>

  <guideline>
    ## 测试策略指导
    - **边界值测试**：重点测试边界条件和极端情况
    - **异常场景覆盖**：确保异常处理逻辑得到充分测试
    - **性能基准验证**：关键功能必须通过性能基准测试
    - **安全测试集成**：在测试中集成基础的安全检查
    - **测试文档同步**：测试用例与功能需求保持同步
  </guideline>

  <process>
    ## 测试实施标准流程
    
    ### Phase 1: 测试规划 (10分钟)
    ```mermaid
    flowchart TD
        A[需求分析] --> B[测试范围确定]
        B --> C[测试类型选择]
        C --> D[测试环境准备]
        D --> E[测试数据准备]
    ```
    
    **规划要点**：
    - 基于架构文档和功能需求确定测试范围
    - 选择适合的测试类型和测试工具
    - 准备独立的测试环境和测试数据
    - 制定测试执行计划和验收标准
    
    ### Phase 2: 单元测试 (40分钟)
    ```mermaid
    flowchart TD
        A[函数级测试] --> B[类级测试]
        B --> C[模块级测试]
        C --> D[覆盖率检查]
        D --> E[边界测试]
    ```
    
    **单元测试策略**：
    - **函数测试**：测试每个函数的输入输出和边界条件
    - **类测试**：测试类的方法和属性，验证对象行为
    - **模块测试**：测试模块内部逻辑和接口契约
    - **覆盖率验证**：确保代码覆盖率达到80%以上
    
    ### Phase 3: 集成测试 (30分钟)
    ```mermaid
    flowchart TD
        A[模块间集成] --> B[API接口测试]
        B --> C[数据流测试]
        C --> D[外部依赖测试]
        D --> E[端到端验证]
    ```
    
    **集成测试重点**：
    - **模块协作**：验证不同模块间的协作是否正常
    - **接口契约**：测试API接口的输入输出格式和错误处理
    - **数据一致性**：验证数据在系统中的流转和处理
    - **外部集成**：测试与外部服务和数据库的集成
    
    ### Phase 4: 性能和安全测试 (20分钟)
    ```mermaid
    flowchart TD
        A[性能基准测试] --> B[负载测试]
        B --> C[安全漏洞扫描]
        C --> D[输入验证测试]
        D --> E[错误处理测试]
    ```
    
    **专项测试内容**：
    - **性能测试**：验证关键功能的响应时间和吞吐量
    - **负载测试**：测试系统在高负载下的表现
    - **安全测试**：检查常见的安全漏洞和攻击向量
    - **容错测试**：验证系统的错误处理和恢复能力
    
    ## 测试类型详细说明
    
    ### 单元测试最佳实践
    ```mermaid
    graph TD
        A[测试用例设计] --> B[正常路径测试]
        A --> C[边界条件测试]
        A --> D[异常情况测试]
        B --> E[断言验证]
        C --> E
        D --> E
    ```
    
    **单元测试清单**：
    - [ ] **正常流程**：测试函数在正常输入下的行为
    - [ ] **边界值**：测试最小值、最大值、空值等边界情况
    - [ ] **异常处理**：测试错误输入和异常情况的处理
    - [ ] **状态变化**：测试对象状态的正确变化
    - [ ] **副作用**：验证函数是否产生预期的副作用
    
    ### 集成测试策略
    ```mermaid
    graph TD
        A[自底向上] --> B[模块组合测试]
        C[自顶向下] --> D[接口驱动测试]
        E[大爆炸] --> F[全系统集成]
        B --> G[集成验证]
        D --> G
        F --> G
    ```
    
    **集成测试重点**：
    - **数据传递**：验证模块间数据传递的正确性
    - **接口契约**：确保接口定义与实现一致
    - **错误传播**：测试错误在系统中的传播和处理
    - **事务一致性**：验证跨模块事务的一致性
    
    ### 性能测试基准
    ```mermaid
    graph TD
        A[响应时间] --> B[< 200ms 快速响应]
        A --> C[< 1s 正常响应]
        A --> D[< 3s 可接受响应]
        E[吞吐量] --> F[并发用户数]
        E --> G[每秒请求数]
        H[资源使用] --> I[CPU使用率]
        H --> J[内存占用]
    ```
    
    **性能指标要求**：
    - **响应时间**：API接口响应时间<1秒，关键查询<200毫秒
    - **吞吐量**：支持预期的并发用户数和请求量
    - **资源使用**：CPU使用率<80%，内存使用合理
    - **稳定性**：长时间运行无内存泄漏和性能退化
  </process>

  <criteria>
    ## 测试质量评价标准
    
    ### 测试覆盖率 (40%)
    - ✅ 单元测试覆盖率≥80%
    - ✅ 关键业务逻辑100%覆盖
    - ✅ 分支覆盖率≥70%
    - ✅ 异常路径覆盖完整
    
    ### 测试有效性 (30%)
    - ✅ 测试用例能够发现真实问题
    - ✅ 边界条件和异常场景覆盖
    - ✅ 测试断言准确有效
    - ✅ 测试数据代表性强
    
    ### 测试自动化 (20%)
    - ✅ 所有测试可自动化执行
    - ✅ 测试结果可重复验证
    - ✅ 持续集成集成完整
    - ✅ 测试报告清晰详细
    
    ### 性能安全 (10%)
    - ✅ 性能基准测试通过
    - ✅ 安全漏洞扫描通过
    - ✅ 负载测试满足要求
    - ✅ 错误处理测试完整
    
    ### 测试交付标准
    - **测试文档**：完整的测试计划和测试报告
    - **测试代码**：高质量的测试用例代码
    - **自动化脚本**：可执行的自动化测试脚本
    - **性能报告**：详细的性能测试结果报告
  </criteria>
</execution>
