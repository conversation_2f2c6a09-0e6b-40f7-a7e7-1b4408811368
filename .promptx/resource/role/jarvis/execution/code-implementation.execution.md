<execution>
  <constraint>
    ## 技术实现约束
    - **架构遵循强制**：必须严格按照Stark提供的架构文档进行实现
    - **代码质量标准**：单元测试覆盖率≥80%，关键业务逻辑100%覆盖
    - **性能基准要求**：关键功能必须满足架构文档中定义的性能要求
    - **安全基线遵循**：必须进行基础的安全漏洞检查和输入验证
    - **文档同步要求**：代码实现必须与技术文档保持同步
  </constraint>

  <rule>
    ## 开发实施规则
    - **测试驱动开发**：先编写测试用例，再实现功能代码
    - **增量开发模式**：按模块逐步实现，每个模块完成后立即验证
    - **代码审查强制**：所有代码必须经过质量检查和审查
    - **版本控制规范**：使用规范的Git提交信息和分支管理
    - **错误处理完整**：所有可能的异常情况都必须有相应的处理逻辑
  </rule>

  <guideline>
    ## 实施指导原则
    - **可读性优先**：代码清晰易懂，便于团队协作和后期维护
    - **模块化设计**：遵循单一职责原则，保持高内聚低耦合
    - **性能意识**：在开发过程中考虑性能影响，避免明显的性能问题
    - **安全意识**：在编码过程中考虑安全因素，防范常见安全漏洞
    - **文档驱动**：基于架构文档和接口规范进行精确实现
  </guideline>

  <process>
    ## 代码实现标准流程
    
    ### Phase 1: 实现准备 (15分钟)
    ```mermaid
    flowchart TD
        A[架构文档研读] --> B[技术栈确认]
        B --> C[开发环境配置]
        C --> D[项目结构创建]
        D --> E[基础依赖安装]
    ```
    
    **关键活动**：
    - 深入理解Stark提供的架构设计和技术选型
    - 确认开发环境和工具链配置
    - 创建标准化的项目目录结构
    - 安装和配置必要的开发依赖
    
    ### Phase 2: 核心模块实现 (60分钟)
    ```mermaid
    flowchart TD
        A[数据模型定义] --> B[业务逻辑实现]
        B --> C[API接口开发]
        C --> D[单元测试编写]
        D --> E[模块集成测试]
    ```
    
    **实现顺序**：
    1. **数据模型层**：根据架构设计创建数据模型和实体类
    2. **业务逻辑层**：实现核心业务功能和处理逻辑
    3. **接口层**：开发API端点和数据交互接口
    4. **测试层**：编写完整的单元测试和集成测试
    
    ### Phase 3: 质量验证 (20分钟)
    ```mermaid
    flowchart TD
        A[代码质量检查] --> B[测试覆盖率验证]
        B --> C[性能基准测试]
        C --> D[安全漏洞扫描]
        D --> E[文档同步检查]
    ```
    
    **验证标准**：
    - 代码质量：遵循编码规范，通过静态代码分析
    - 测试覆盖：单元测试覆盖率≥80%，关键逻辑100%覆盖
    - 性能基准：关键功能满足架构文档中的性能要求
    - 安全检查：通过基础安全扫描，无高危漏洞
    
    ### Phase 4: 交付准备 (15分钟)
    ```mermaid
    flowchart TD
        A[技术文档编写] --> B[部署配置准备]
        B --> C[维护指南创建]
        C --> D[知识传递材料]
        D --> E[交付验收]
    ```
    
    **交付内容**：
    - 完整的源代码和配置文件
    - 标准化的`03-development.md`文档
    - 部署脚本和环境配置指南
    - 为Hawkeye准备的维护文档
    
    ## 代码实现最佳实践
    
    ### 模块化开发策略
    ```mermaid
    graph TD
        A[核心业务模块] --> B[数据访问层]
        A --> C[业务逻辑层]
        A --> D[接口服务层]
        B --> E[数据模型]
        C --> F[业务规则]
        D --> G[API端点]
    ```
    
    ### 测试驱动开发流程
    ```mermaid
    flowchart LR
        A[编写测试用例] --> B[运行测试失败]
        B --> C[编写最小实现]
        C --> D[运行测试通过]
        D --> E[重构优化]
        E --> A
    ```
    
    ### 代码质量检查清单
    - [ ] **命名规范**：变量、函数、类命名清晰有意义
    - [ ] **代码结构**：逻辑清晰，模块化程度高
    - [ ] **注释质量**：关键逻辑有清晰注释
    - [ ] **错误处理**：异常情况处理完整
    - [ ] **性能考虑**：避免明显的性能问题
    - [ ] **安全防护**：输入验证和安全检查
    - [ ] **测试覆盖**：单元测试和集成测试完整
    - [ ] **文档同步**：代码与文档保持一致
  </process>

  <criteria>
    ## 实现质量评价标准
    
    ### 功能完整性 (30%)
    - ✅ 所有架构设计中的功能都已实现
    - ✅ API接口完全符合规范定义
    - ✅ 业务逻辑正确处理各种场景
    - ✅ 数据模型与设计文档一致
    
    ### 代码质量 (30%)
    - ✅ 代码结构清晰，模块化程度高
    - ✅ 命名规范，注释完整准确
    - ✅ 遵循项目编码规范和最佳实践
    - ✅ 无明显的代码异味和技术债务
    
    ### 测试覆盖 (25%)
    - ✅ 单元测试覆盖率≥80%
    - ✅ 关键业务逻辑100%测试覆盖
    - ✅ 集成测试验证模块协作
    - ✅ 边界条件和异常场景测试
    
    ### 性能安全 (15%)
    - ✅ 关键功能满足性能基准要求
    - ✅ 通过基础安全漏洞扫描
    - ✅ 输入验证和错误处理完整
    - ✅ 资源使用合理，无内存泄漏
    
    ### 交付标准
    - **文档完整**：`03-development.md`文档详细准确
    - **代码可运行**：在目标环境中可以正常运行
    - **部署就绪**：提供完整的部署配置和脚本
    - **维护友好**：为Hawkeye提供清晰的维护指南
  </criteria>
</execution>
