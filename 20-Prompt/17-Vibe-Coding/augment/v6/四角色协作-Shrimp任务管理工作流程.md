# 四角色协作 - Shrimp任务管理工作流程

## 📋 概述

本文档定义了基于 shrimp-video-factory 的四角色协作工作流程，确保Analyst、Architect、Developer、Maintainer四个专业角色能够高效协作，通过统一的任务管理系统实现项目的全生命周期管理。

## 🎯 核心原则

### 任务驱动协作
- 所有角色通过shrimp任务系统进行协作
- 任务状态实时同步，确保协作透明
- 依赖关系自动管理，避免工作阻塞

### 质量门禁机制
- 每个阶段必须通过`verify_task_shrimp-video-factory`验证（≥80分）
- 上游任务完成后才能开始下游工作
- 重要决策通过任务系统记录和追溯

### 知识传承体系
- 使用`promptx_remember`记录角色专业经验
- 通过任务系统沉淀项目知识
- 建立可复用的最佳实践库

## 🔄 标准工作流程

### 阶段1：需求分析（Analyst主导）

#### 任务创建
```bash
# 激活Analyst角色
promptx_action analyst

# 创建项目主任务
plan_task_shrimp-video-factory "项目需求分析：[项目名称] - 深度挖掘用户需求，编写完整需求文档"

# 分解需求分析任务
split_tasks_shrimp-video-factory [主任务ID]
```

#### 核心任务
- **需求挖掘任务**：深度挖掘用户真实需求
- **可行性分析任务**：评估需求的技术可行性
- **文档编写任务**：编写标准化需求文档
- **风险评估任务**：识别需求实现风险

#### 交付标准
- 完成`01-requirements.md`文档
- 所有需求任务通过`verify_task_shrimp-video-factory`验证
- 为Architect角色建立任务依赖关系

### 阶段2：架构设计（Architect主导）

#### 任务接收
```bash
# 激活Architect角色
promptx_action architect

# 查看需求分析成果
list_tasks_shrimp-video-factory completed
get_task_detail_shrimp-video-factory [需求分析任务ID]
```

#### 核心任务
- **技术调研任务**：使用`research_mode_shrimp-video-factory`进行技术选型
- **架构设计任务**：设计系统整体架构
- **模块设计任务**：设计模块划分和接口
- **方案验证任务**：使用`reflect_task_shrimp-video-factory`验证架构方案

#### 交付标准
- 完成`02-architecture.md`文档
- 技术选型有充分的调研支撑
- 为Developer角色提供清晰的实现指导

### 阶段3：代码实现（Developer主导）

#### 任务接收
```bash
# 激活Developer角色
promptx_action developer

# 查看架构设计成果
get_task_detail_shrimp-video-factory [架构设计任务ID]
```

#### 核心任务
- **核心模块开发**：实现系统核心功能模块
- **接口实现任务**：实现模块间接口
- **测试用例任务**：编写单元测试和集成测试
- **性能优化任务**：优化代码性能和质量

#### 交付标准
- 完成`03-development.md`文档和源代码
- 测试覆盖率达到80%以上
- 所有模块通过`verify_task_shrimp-video-factory`质量验证

### 阶段4：维护优化（Maintainer主导）

#### 任务接收
```bash
# 激活Maintainer角色
promptx_action maintainer

# 全面回顾项目历史
list_tasks_shrimp-video-factory all
query_task_shrimp-video-factory "completed"
```

#### 核心任务
- **文档维护任务**：更新和同步项目文档
- **性能监控任务**：监控系统运行状态
- **用户反馈任务**：处理用户反馈和改进需求
- **持续改进任务**：基于数据分析提供改进建议

#### 交付标准
- 完成`04-maintenance.md`文档
- 建立系统监控和维护机制
- 提供项目改进建议和最佳实践

## 🔗 角色协作机制

### 依赖关系管理
```
Analyst → Architect → Developer → Maintainer
   ↓         ↓          ↓          ↓
需求文档   架构文档    源代码     维护方案
```

### 并行协作场景
- **需求澄清阶段**：Analyst与Architect可并行工作
- **模块开发阶段**：Developer可按模块并行开发
- **持续维护阶段**：Maintainer可与其他角色并行工作

### 反馈循环机制
- **向上反馈**：下游角色发现问题时，及时反馈给上游角色
- **横向协作**：同级角色通过任务系统进行信息共享
- **经验沉淀**：所有角色将经验记录到任务系统和记忆体系

## 📊 质量控制体系

### 任务验证标准
- **完整性检查**：任务输出是否完整
- **质量评估**：是否达到预定质量标准
- **依赖满足**：是否满足下游角色的输入要求
- **文档同步**：文档是否与实际工作同步

### 协作效率指标
- **任务完成率**：按时完成任务的比例
- **质量通过率**：首次通过verify_task_shrimp-video-factory的比例
- **依赖阻塞率**：因依赖问题导致的工作阻塞
- **知识复用率**：历史经验的复用程度

## 🛠️ 工具使用规范

### 必用工具清单
- **`plan_task_shrimp-video-factory`**：创建项目和阶段主任务
- **`split_tasks_shrimp-video-factory`**：分解复杂任务为可管理的子任务
- **`execute_task_shrimp-video-factory`**：执行具体工作任务
- **`verify_task_shrimp-video-factory`**：验证任务完成质量
- **`update_task_shrimp-video-factory`**：更新任务状态和成果

### 协作工具清单
- **`list_tasks_shrimp-video-factory`**：查看任务状态和进度
- **`query_task_shrimp-video-factory`**：搜索和分析历史任务
- **`get_task_detail_shrimp-video-factory`**：获取任务详细信息
- **`process_thought_shrimp-video-factory`**：处理复杂问题的深度思考
- **`research_mode_shrimp-video-factory`**：进行技术调研和方案比较

### 记忆管理工具
- **`promptx_remember`**：记录角色专业经验
- **`promptx_recall`**：回忆相关历史经验
- **`promptx_action`**：角色切换和激活

## ⚠️ 注意事项

### 任务管理规范
- 任务粒度控制在1-2个工作天内完成
- 重要决策必须通过任务系统记录
- 任务状态变更要及时同步
- 质量验证必须达到80分以上

### 协作沟通规范
- 通过任务系统进行正式沟通
- 重要变更要通知相关角色
- 保持文档与代码的同步
- 建立定期的协作回顾机制

### 知识管理规范
- 及时记录重要经验和教训
- 建立可复用的模式和模板
- 定期回顾和优化工作流程
- 将成功实践转化为标准规范

---

**版本**：v1.0  
**创建时间**：2025-08-01  
**适用范围**：一人公司多角色开发范式系统
