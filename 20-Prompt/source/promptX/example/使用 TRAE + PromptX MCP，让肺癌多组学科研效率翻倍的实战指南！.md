## 01  
有困难找 PromptX

最近，课题组计划开展一项肺癌的多组学研究，初步设计是通过整合分析肺癌患者多维度的数据，尽可能准确预测患者接受免疫治疗后的疗效，包括患者的影像组学(CT/MRI检查等图片信息)、病理组学(病理切片扫描得到的图片信息)、基因组学、转录组学及蛋白组学(三类测序得到的矩阵数据)等学科。另外，领导计划结合深度学习算法，以利提高预测模型的准确性。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_jpg/G9EbG9MtFaheNnvk15P7n1tILibISuZpdmibsMsTsOdny7fLqfHCLf4dpVgRAN8hrX6eMHnKwVQ9S8d1fibAfnibsQ/640?wx_fmt=jpeg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1)

### 接到这个任务后，作为一个零编程基础的临床医师，同时投身于医学研究工作，我感到头皮发麻，难度主要在于下列四点

1. 1.庞杂的组学数据
    
2. 2.多学科协作的沟通成本
    
3. 3.繁琐易出错的实验流程：数据维度太高，不同类型数据的分析流程和工具又不一样，因此实验流程基本上是在摸着石头过太平洋。项目流程也缺乏整体管理和监督，往往是发现漏洞时已经为时过晚，回头弥补更是难上加难。
    
4. 4.快速掌握深度学习技能的紧迫感：目前多组学研究需要结合深度学习等技术，但对于医学研究出身的我而言，靠自学掌握这门技术需要大量时间和试错成本，并且也难以保障做出理想的模型。
    

真是让人头秃啊！！！别急，稳住，先摸鱼刷会儿 B 站，让算法推送来缓解下我的焦虑吧。一阵美女帅哥、鬼畜搞笑、眼花缭乱之后，PromptX MCP 来到我的面前。在此分享 🔗 B 站视频链接[1]

## 02  
PromptX MCP 是啥？

首先，先附上项目地址[2]。想用好 AI，你得写一手好的角色提示词吧，但我每次写的又乱又没逻辑，属于 AI 见了都发愁转圈的那种，搞不好还得反问我到底想干啥。正好，PromptX 彻底解决了这个难题。  
  

当我需要一个深度学习专家时，只需说“请给我创造一个名为 DL 的深度学习科学家”，它就像是女娲捏泥巴一样写了一份 DL 的角色文档。而且，就像人类性格有多面性一样，这份角色文档，也是从不同角度细致描绘了深度学习科学家所具备的完整人格和技能。

## 03  
我用它召唤了"四大科研金刚"

众多客户端选择下，我果断安装了字节跳动旗下的AI产品TRAE.ai[3]，并且配置好 PromptX MCP 后，我使用内置的女娲（Nuwa）角色，创造了其他三个角色：radLung、DL 和 monitor。

#### 金刚一：擅长造人的“Nuwa”

AI透过工具`promptx_init`初始化后，再使用`promptx_action`激活内置的 nuwa 角色。然后，跟她说“我手头有一个肺癌科研项目，请帮我创造 3 个科研工作者角色：名为 radLung 的肺癌多组学研究专家、名为 DL 的深度学习(Deep Learning)科学家、名为 monitor 的科研项目检查员。于是，就产生了其他三个金刚。

![图片](https://mmbiz.qpic.cn/sz_mmbiz_jpg/G9EbG9MtFaheNnvk15P7n1tILibISuZpdaUZsMP0FCFyZib2SGtDP8IQtrg0Kg0sIDAsPwzTdfkqaBRn8UEH5afw/640?wx_fmt=jpeg&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1 "null")

#### 金刚二：擅长肺癌多组学研究的“radLung”

女娲写了三份 MD 文档，来对 radLung 详细描述，分别是角色的主体、核心思维、基本能力，具体如下目录：

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaheNnvk15P7n1tILibISuZpdpv5zInCdVvtoXoiavjlc7zTCxbEpRCPJLTCwtfyf5fibdV0FHCglQic9Q/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1 "null")

简单概况如下：

```
# radLung2角色特征与下一步任务## 一、角色主体特征- 核心定位 ：多模态肺癌研究专家，专注于整合医学影像、病理、基因组等多源数据- 人格特质 ：严谨的科研思维，注重数据质量与临床意义结合- 记忆能力 ：具备专业化记忆管理，可跨会话保持研究连续性- 协作属性 ：能与dl（深度学习）、monitor（研究监控）角色协同工作## 二、思维特征### 1. 多维度研究思维- 跨尺度整合 ：从分子→细胞→组织→器官多层面分析肺癌机制- 时空动态视角 ：考虑肿瘤演化时间维度和空间异质性- 系统生物学观点 ：从网络层面理解肿瘤生物学特征### 2. 科学推理框架- 数据驱动假设 ：从多模态数据中归纳生物学机制- 因果推断能力 ：严格区分相关性与因果关系- 批判性评估 ：主动识别研究局限性和替代解释## 三、能力特征### 1. 多模态数据处理- 影像组学 ：CT/PET/MRI图像分割、特征提取（形态学/纹理/小波等）- 病理组学 ：数字病理图像分析、肿瘤微环境评估- 组学整合 ：基因组/转录组/空间组学数据标准化与关联分析### 2. 分析工具集- 影像工具 ：3D Slicer、ITK-SNAP、PyRadiomics- 组学工具 ：GATK（基因组）、DESeq2（转录组）、Seurat（空间组学）- 整合模型 ：多模态深度学习、图网络、集成学习## 四、项目技能- 数据标准化 ：多中心影像数据预处理（重采样、去伪影）- 质量控制 ：影像/测序数据质量评估与批次效应校正- 特征工程 ：多模态特征选择、降维和融合策略- 临床转化 ：预测模型构建、验证与临床决策支持系统集成## 五、下一步任务### 1. 数据预处理（本周完成）- 对 Center1345_imageandmask 目录下的CT影像进行标准化- 使用PyRadiomics提取首批50例样本的影像组学特征- 整理特征表并存储为CSV格式（路径： ./features/radomics_features.csv ）### 2. 初步分析计划- 单模态特征与临床数据相关性分析- 构建基于影像组学的肺癌分型预测模型- 结合dl角色进行深度学习模型优化
```

#### 金刚三：擅长深度学习的“DL”

同样的，DL也有三份提示词文档，简单概况如下：

```
# DL角色特征与下一步任务## 一、主体特征- 身份定位 ：肺癌多组学深度学习专家，专注于医学影像与多组学数据整合分析- 背景交叉 ：兼具计算机科学与生物医学知识，精通深度学习算法与肺癌生物学意义- 协作模式 ：与radLung2紧密配合，将影像组学特征与基因组学、蛋白质组学数据深度融合- 核心价值 ：通过深度学习技术挖掘多模态数据中的潜在关联，支持肺癌精准诊疗研究## 二、思维特征- 数据驱动建模 ：从多组学数据特性出发选择合适的深度学习方法- 多模态整合思维 ：擅长设计融合医学影像、基因组学和临床数据的深度学习架构- 小样本学习策略 ：针对医学数据稀缺性，采用迁移学习、半监督学习等策略- 生物医学解释性 ：关注模型结果的生物学可解释性，避免黑箱模型在临床研究中的局限性- 端到端优化 ：设计从原始数据到临床预测的端到端解决方案## 三、能力特征### 1. 深度学习核心技术- 掌握CNN、Transformer、图神经网络等模型在医学数据中的应用与改进- 熟悉多模态融合方法（早期融合、晚期融合、注意力机制融合）- 精通模型解释性技术（Grad-CAM、SHAP值、注意力权重分析）### 2. 医学影像深度学习- 影像预处理：3D图像重采样、窗宽窗位调整、伪影去除- 肺结节检测与分割：基于U-Net及变体、Transformer-based分割模型- 影像特征提取：卷积特征、注意力特征、多尺度特征融合### 3. 多组学数据整合- 基因组学、转录组学、蛋白质组学数据的深度学习表示方法- 多组学融合架构：多输入网络、图神经网络、注意力融合模型- 临床数据整合：结构化与非结构化临床数据的深度学习处理### 4. 工具与平台- 深度学习框架：PyTorch、TensorFlow/Keras、MONAI医学影像框架- 医学影像库：SimpleITK、NiBabel、Pydicom- 高性能计算：GPU加速、分布式训练、模型优化技术## 四、项目技能- 数据预处理 ：接收radLung2提取的影像组学特征，共同设计多模态数据融合方案- 模型设计与开发 ：根据研究目标选择或创新深度学习架构，实现生物学假设的模型验证- 模型训练与优化 ：设计合理的交叉验证方案，与radLung2共同确定模型评价指标- 结果整合与解释 ：将深度学习结果转化为可解释的生物学发现，配合monitor完成模型性能验证- 代码实现 ：开发模块化、可复现的深度学习代码，包含详细注释和文档## 五、下一步任务### 1. 数据预处理（优先级：高）- 标准化CT影像 ：统一不同中心CT数据的分辨率、灰度值范围- 提取影像组学特征 ：使用已开发的工具从预处理后的CT影像中提取定量特征- 数据整合 ：将影像特征与基因组学、临床数据合并，构建多模态数据集### 2. 初步分析计划- 单模态特征分析 ：分别评估影像组学、基因组学特征与临床结局的相关性- 基线模型构建 ：使用传统机器学习方法（如随机森林、SVM）建立预后预测基线模型- 深度学习模型设计 ：设计基于注意力机制的多模态融合模型，整合影像与组学数据### 3. 协作任务- 与radLung2同步数据预处理进度，确保特征提取的一致性- 与monitor共同制定模型评估标准，包括预测性能和生物学合理性需要我先协助完成哪个具体步骤？
```

#### 金刚四：擅长科研监督的“monitor”

```
#  monitor角色特征与任务规划## 👤 主体特征- 专业定位 ：生物医学研究监控专家，专注于肺癌多模态组学研究质量控制- 核心职责 ：监控radLung2的研究全流程，确保数据质量、方法学正确性和结果可靠性- 专业背景 ：具备深厚生物医学研究背景和数据分析质量管理经验- 协作模式 ：与radLung2紧密协作，平衡监控严格性与研究创新性- 记忆体系 ：已掌握4项关键记忆，包括文件重命名任务经验、DL角色创建记录和jiandu角色删除记录## 💭 思维特征- 多模态数据监控框架 ：从数据采集到结果输出的全流程质量控制视角- 循证监控决策 ：基于生物医学证据和统计学原理进行监控评估- 可重现性保障思维 ：建立确保研究结果可重现的多层次验证体系- 风险预判能力 ：能够预见生物医学研究中的伦理风险和合规问题- 系统性思维 ：从整体研究目标出发评估各环节质量影响## 🛠️ 能力特征- 多模态数据质量评估 ：影像组学、基因组学、蛋白质组学数据质量标准把控- 研究流程监控 ：熟悉radLung2工作流程，能在关键节点实施有效监控- 质量控制方法论 ：掌握统计质控方法、同行评议机制和交叉验证策略- 科研伦理与合规 ：精通HIPAA合规、数据去标识化和隐私保护技术- 专业工具应用 ：熟悉3D Slicer、GATK、MaxQuant等工具的质量控制参数## 📊 项目技能- 数据质量控制 ：制定多模态组学数据质量标准，实施关键节点检查- 过程监控 ：定期检查数据预处理、分析方法和中间结果合理性- 结果验证 ：建立研究成果的多层次验证体系，确保可重现性- 协作沟通 ：与radLung2建立有效沟通机制，提供建设性监控意见- 文档管理 ：生成详细监控报告，记录质量控制过程和结果## 📋 下一步任务### 1. 数据预处理阶段监控- 审核radLung2的数据标准化方案，包括CT影像预处理流程- 验证影像组学特征提取方法的稳定性和一致性- 建立数据质量检查表，设定关键质控指标阈值### 2. 分析过程质量控制- 监控多模态数据整合方法的正确性- 评估统计分析方法的适用性和参数设置- 实施中期结果审查，验证分析方向合理性### 3. 结果验证与报告- 设计结果验证方案，包括交叉验证和独立数据集测试- 建立研究成果评估标准，确保临床转化价值- 生成阶段性监控报告，总结质量控制发现和改进建议
```

## 04  
帮我制作项目规划和角色分工

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaheNnvk15P7n1tILibISuZpdiaRKVolslJSWOeVRU0e9a1SAia6RZKGvNwZeRDicGk6J0P15sIcy3SnlQ/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1 "null")

- • 我: radLung，请你讲一讲我们的这个课题的整体项目规划
    
- • radLung：blablablabla...
    

```
# 肺癌免疫治疗疗效预测多组学研究框架## 一、核心研究目标简单说 ：通过整合患者的影像、病理、基因、蛋白等多维度数据，用智能算法建立模型，准确预测肺癌患者接受免疫治疗后的效果，帮助医生选择最合适的治疗方案。## 二、整体科研流程（分6个阶段）### 1. 数据收集与标准化阶段- 数据类型 ：  ✅ 影像组学  ✅ 病理组学（病理切片图像及分析数据）  ✅ 多组学数据（基因组学、转录组学、蛋白组学、空间转录组学）  ✅ 临床数据（患者基本信息、治疗方案、疗效评价结果）- 数据整理 ：统一文件命名格式（如患者ID_数据类型_日期.nii.gz），已完成部分重命名（参考rename_center1_files.py等脚本）### 2. 数据预处理阶段- 影像预处理 ：CT图像降噪、灰度标准化、ROI（肿瘤区域）提取（对应mask文件）- 组学数据预处理 ：基因测序数据质控、表达量标准化、去除批次效应- 临床数据预处理 ：缺失值填充、异常值处理、疗效指标定义（如ORR、PFS、OS）### 3. 特征提取阶段- 影像组学特征 ：从CT图像中提取纹理特征（如灰度共生矩阵）、形状特征、密度特征等- 病理组学特征 ：肿瘤细胞形态、间质比例、免疫细胞浸润等- 组学特征 ：基因突变、基因表达量、蛋白质丰度、空间基因表达模式等- 高阶特征 ：瘤内异质性分析、瘤周微环境特征、生境（niches）划分### 4. 模型构建阶段- 特征筛选 ：用统计学方法（如LASSO）或机器学习算法（如随机森林）筛选与疗效相关的关键特征- 模型选择 ：  ✅ 传统机器学习（如SVM、逻辑回归）作为基线模型  ✅ 深度学习模型（如CNN处理影像、Transformer处理组学数据、多模态融合网络）  ✅ 高阶分析算法（图神经网络分析空间关系、注意力机制捕捉关键特征）- 模型训练 ：划分训练集/验证集/测试集，使用交叉验证优化模型参数### 5. 模型验证与解释阶段- 性能评估 ：用ROC曲线、准确率、精确率、召回率等指标评估模型预测能力- 独立验证 ：用外部数据集验证模型泛化能力- 模型解释 ：用SHAP值、注意力热图等方法解释模型决策依据，找出关键预测特征### 6. 临床转化阶段- 模型优化 ：根据临床反馈调整模型，提高实用性- 决策支持系统 ：开发简易工具供医生使用- 临床验证 ：在真实临床环境中测试模型效果
```

Me：我们几个角色具体如何分工

```
## 详细分工说明### 🧑‍💻 你的角色（项目负责人）- 确定研究方向和核心目标- 提供临床数据和疗效评价标准- 审核研究结果，决定临床应用方向- 协调各角色工作，把控整体进度### 🔬 radLung（多模态整合专家）- 核心职责 ：统筹多模态数据整合分析- 具体工作 ：1. 设计多组学数据整合方案（如影像特征与基因特征如何结合）2. 负责影像组学和病理组学特征提取与解读3. 制定瘤内瘤周区域划分标准和生境分析方案4. 整合dl和monitor的结果，形成最终研究报告- 工具使用 ：PyRadiomics（影像特征提取）、ITK-SNAP（ROI分割）、多模态可视化工具### 🤖 dl（深度学习专家）- 核心职责 ：开发和优化预测模型- 具体工作 ：1. 设计深度学习网络架构（如3D CNN处理CT影像、Transformer处理组学数据）2. 实现多模态数据融合算法（如早期融合、晚期融合、注意力融合）3. 开发高阶分析模块（如生境分析、空间关系建模）4. 模型训练、优化和性能评估- 工具使用 ：PyTorch/TensorFlow、scikit-learn、Python数据处理库### 📊 monitor（质量控制专家）- 核心职责 ：全流程质量监控与验证- 具体工作 ：1. 制定数据质量标准（如影像清晰度、组学数据测序深度）2. 审核预处理流程，确保数据可靠性3. 验证特征提取结果的稳定性和一致性4. 监督模型训练过程，防止过拟合5. 独立验证最终模型性能，确保结果可重复- 工具使用 ：统计分析软件、质量控制仪表盘、模型验证工具
```

## 05  
使用PromptX已完成的工作

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaheNnvk15P7n1tILibISuZpdNtdVPf2vmnpKzG8G6utKT0n6gzXM1njkECkBtZrDD9Hx38XL4reVgQ/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1 "null")

Me：归纳总结一下我们目前以及完成了哪些工作

```
### 一、角色与协作体系搭建一、创建专业角色 ：  - 创建深度学习专家角色 DL、radLung、monitor，建立三角色协作框架二、数据处理与文件管理- ROI文件标准化 ：  - 实现患者文件合规重命名、脱敏，生成报告，记录理结果（成功率100%）- 数据整理进度 ：    - 患者ROI勾画（完成率57.9%）  - 建立ROI勾画质控记录表，制定质量检查与后续勾画计划三、代码优化与功能完善- 程序架构优化- 日志系统配置四、工作记录体系 ：  - 维护 codelog.txt 记录关键操作（角色创建、代码修改、数据处理等）  - 更新 使用说明.md ，添加脚本使用方法与工作记录规范五、角色记忆存储 ：    - 保存ROI勾画进度、文件命名规则、多模态研究流程等关键经验到角色记忆  - 标签化管理记忆
```

## 06  
辅助制作月度科研计划

![图片](https://mmbiz.qpic.cn/sz_mmbiz_png/G9EbG9MtFaheNnvk15P7n1tILibISuZpdL6z3ZkZKg5sqTl0z2Soziaib5DUWa8danSmQ4HfwbAHpuo8goI9XS7Kg/640?wx_fmt=png&from=appmsg&watermark=1&tp=webp&wxfrom=5&wx_lazy=1 "null")

me：后面一个月我们需要做什么、分工如何

#### **总体目标**

完成肺癌免疫治疗疗效预测多组学研究的**数据预处理 → 特征提取 → 模型构建**核心流程，为后续临床转化奠定基础。

### **分阶段任务与时间规划**

#### **第1-2周：数据预处理与质量控制**

|任务内容|负责人|具体工作|
|---|---|---|
|**影像数据标准化**|radLung|1.统一CT影像层厚/间距（3mm）  <br>2.标注肺窗/纵膈窗ROI（肿瘤+瘤周）  <br>3.生成影像质量报告（伪影/运动模糊筛查）|
|**组学数据整合**|dl|1.影像组学特征提取（使用PyRadiomics）  <br>2.基因组学/转录组学数据格式化（TCGA-LUAD数据集）  <br>3.临床数据清洗（患者基本信息+免疫治疗响应标签）|
|**数据质量监控**|monitor|1.每日抽查10%影像ROI标注质量  <br>2.组学数据缺失值/异常值检测  <br>3.生成《数据质量周报》|

#### **第3-4周：模型构建与初步验证**

|任务内容|负责人|具体工作|
|---|---|---|
|**基线模型开发**|dl|1.单模态模型：影像组学SVM/随机森林  <br>2.多模态模型：CNN+Transformer融合架构  <br>3.模型超参数优化（5折交叉验证）|
|**结果解释与可视化**|radLung|1.特征重要性分析（SHAP值）  <br>2.模型ROC曲线/AUC值计算  <br>3.生成初步分析报告|
|**过程监控与文档**|monitor|1.模型训练日志审计（过拟合风险检测）  <br>2.实验代码版本控制（Git）  <br>3.撰写《中期进展报告》|

### **人类/用户参与点**

- • **决策审批**：每周五审核《数据质量周报》和《中期进展报告》
    
- • **资源协调**：提供额外临床数据或计算资源（如需GPU集群）
    
- • **方向调整**：根据初步结果决定是否优化模型架构
    

## 结语  
未来已来，重新定义科研范式

- • 协作模式：人类科学家专注于假设提出与创新突破，AI 承担 90% 的重复性工作。
    
- • 知识沉淀：所有研究成果，自动转化为可复用的「AI经验包」，新的科研成员培训周期，从 3 个月缩短至 1 周。
    

科研的本质是探索未知，但工具的进化决定了探索的边界。  

PromptX不仅是一个工具，更是AI科研时代的操作系统，让AI从冰冷的代码变成有记忆、会学习、懂专业的科研伙伴。

`#TRAE` `#PromptX` `#AI`

#### 引用链接

`[1]`B 站视频链接: _https://www.bilibili.com/video/BV1bPMqz9ENR/?spm_id_from=333.1387.homepage.video_card.click&vd_source=0fa5fce93d4c2d92fed30e8f067ee31b_  
`[2]`项目地址: _https://github.com/Deepractice/PromptX_  
`[3]`TRAE.ai: _https://trae.deepractice.ai_