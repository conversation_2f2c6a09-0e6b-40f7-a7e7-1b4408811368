---
创建: 2025-07-28
---

# Amazon Kiro AI 深度研究报告

## 执行摘要

Amazon Kiro AI 是亚马逊推出的新一代 AI 编程 IDE，基于 VS Code 1.94 构建，核心特色是**规格驱动开发（Spec-Driven Development）**和**智能代理钩子（Agent Hooks）**机制。与传统的代码补全工具不同，Kiro 采用"规划优先"的方法，通过结构化的需求分析、设计文档和任务分解来指导整个开发流程。

## 1. Kiro AI 核心架构

### 1.1 技术基础
- **基础平台**: VS Code 1.94 (Code OSS)
- **AI 模型**: 主要使用 Claude Sonnet 4.0，备用 3.7
- **多模型支持**: OpenAI GPT系列、Anthropic Claude系列、AWS Bedrock、Ollama、Mistral、Gemini
- **扩展机制**: 通过 OpenVSX 生态系统

### 1.2 核心组件
- **Kiro Agent Extension**: 核心 AI 代理扩展
- **Model Context Protocol (MCP)**: 多模态上下文集成
- **Agent Steering System**: 智能代理引导系统
- **Spec Workflow Engine**: 规格驱动工作流引擎

## 2. Spec 模式深度分析

### 2.1 Spec 模式工作机制

Kiro 的 Spec 模式是一个四阶段的结构化开发流程：

#### 阶段1: 需求收集 (Requirements Gathering)
```
输入: 用户的功能想法
处理: EARS格式需求文档生成
输出: .kiro/specs/{feature_name}/requirements.md
```

**核心特征**:
- 使用 EARS (Easy Approach to Requirements Syntax) 格式
- 自动生成用户故事: "As a [role], I want [feature], so that [benefit]"
- 包含验收标准和边界条件
- 迭代式需求精化，直到用户明确批准

#### 阶段2: 设计文档创建 (Design Document Creation)
```
输入: 已批准的需求文档
处理: 技术架构设计和研究
输出: .kiro/specs/{feature_name}/design.md
```

**核心特征**:
- 包含架构概览、组件接口、数据模型
- 支持 Mermaid 图表生成
- 错误处理和测试策略
- 设计决策的理由说明

#### 阶段3: 任务规划 (Implementation Planning)
```
输入: 已批准的设计文档
处理: 可执行任务分解
输出: .kiro/specs/{feature_name}/tasks.md
```

**核心特征**:
- 最多两级层次的复选框任务列表
- 每个任务都是具体的编码活动
- 测试驱动开发优先
- 增量式进度验证

#### 阶段4: 任务执行 (Task Execution)
```
输入: 具体任务请求
处理: 基于上下文的代码生成
输出: 实际代码实现
```

**核心特征**:
- 一次只执行一个任务
- 严格验证需求符合性
- 完成后等待用户审查

### 2.2 Agent Hooks 机制

Agent Hooks 是 Kiro 的自动化执行机制：

**触发机制**:
- 文件保存事件
- 文件修改事件
- 手动触发按钮

**自动化操作**:
- 文档更新 (README、注释)
- 代码质量检查
- 测试用例生成
- 版本控制提交

**配置方式**:
- 自然语言指令定义
- 版本控制存储
- 项目级别配置

### 2.3 多模态上下文集成

**Model Context Protocol (MCP) 特性**:
- 多文件上下文理解
- 图像、URL、终端输出处理
- 持久化项目记忆
- 外部文档服务器集成

## 3. 与竞品对比分析

### 3.1 Kiro vs Cursor

| 特性 | Kiro | Cursor |
|------|------|--------|
| 开发方法 | 规划优先 | 提示驱动 |
| 自动化程度 | 高 (Agent Hooks) | 中 (手动迭代) |
| 上下文保持 | 持久化记忆 | 会话级别 |
| 模型支持 | 主要 Claude | 多模型灵活 |
| 企业集成 | AWS 深度集成 | 通用性强 |

### 3.2 核心差异化优势

1. **结构化工作流**: 强制性的需求-设计-实现流程
2. **自动化维护**: Agent Hooks 减少手动维护工作
3. **企业级集成**: 与 AWS 服务深度整合
4. **持久化上下文**: 跨会话的项目状态保持

## 4. 技术实现细节

### 4.1 多模型编辑提示模板

Kiro 为不同 AI 模型定制了 14 种编辑提示模板：

- **GPT 系列**: 基于前缀-后缀的代码编辑
- **Claude 系列**: 专家程序员角色扮演
- **Mistral/DeepSeek**: 指令式代码重写
- **Llama/Alpaca**: 结构化输入输出格式

### 4.2 动态上下文注入

系统提示中动态注入：
- 操作系统信息 (OS, Platform, Shell)
- 当前工作区状态
- 打开的编辑器文件
- 活动文件信息
- 当前日期时间

### 4.3 Steering 引导机制

通过 `.kiro/steering/*.md` 文件实现：
- **Always included**: 默认包含的团队标准
- **Conditional**: 基于文件匹配的条件包含
- **Manual**: 通过上下文键手动引用

## 5. 开发者反馈与限制

### 5.1 积极反馈
- 减少重复提示工程工作
- 自动化文档维护
- "高级开发者自动驾驶"体验
- 清晰的开发路线图

### 5.2 当前限制
- Claude Sonnet 高负载导致的性能问题
- 瀑布式流程对快速迭代的限制
- 规格与代码同步漂移风险
- 产品表面复杂度过高 (14种编辑模式)

## 6. 编程角色需求分析

基于 Kiro AI 的深度分析，我们识别出当前环境中需要创建的编程类相关角色：

### 6.1 规格驱动开发角色群

#### 🎯 需求分析师 (Requirements Analyst)
- **核心功能**: 专门处理 EARS 格式需求文档，结构化需求收集
- **交互模式**: 苏格拉底式提问，逐步澄清和精化需求
- **输出标准**: 生成符合 EARS 语法的需求文档
- **适用场景**: 项目启动、功能规划、需求变更管理

#### 🏗️ 系统架构师 (System Architect)
- **核心功能**: 技术架构设计、组件关系定义、技术选型决策
- **交互模式**: 基于需求文档进行架构设计，支持 Mermaid 图表生成
- **输出标准**: 包含架构图、接口定义、数据模型的设计文档
- **适用场景**: 系统设计、技术重构、架构评审

#### 📋 任务规划师 (Task Planner)
- **核心功能**: 将设计文档分解为可执行的开发任务
- **交互模式**: 基于设计文档生成任务清单，支持优先级排序
- **输出标准**: 结构化任务列表，包含验收标准和依赖关系
- **适用场景**: 开发计划制定、进度管理、团队协作

### 6.2 智能自动化角色群

#### 🛡️ 代码质量守护者 (Code Quality Guardian)
- **核心功能**: 自动化代码审查、编码标准执行、质量检查
- **交互模式**: 基于代码变更触发，提供改进建议和修复方案
- **输出标准**: 代码质量报告、修复建议、最佳实践推荐
- **适用场景**: 代码提交前检查、持续集成、代码重构

#### 📚 文档同步专家 (Documentation Sync Expert)
- **核心功能**: 保持代码与文档的一致性，自动更新文档
- **交互模式**: 监听代码变更，智能更新相关文档
- **输出标准**: 同步的文档更新、变更日志、文档质量报告
- **适用场景**: API 文档维护、README 更新、技术文档管理

#### 🧪 测试驱动开发助手 (TDD Assistant)
- **核心功能**: 自动生成测试用例、维护测试覆盖率、测试策略设计
- **交互模式**: 基于代码和需求生成测试，支持多种测试框架
- **输出标准**: 完整的测试套件、覆盖率报告、测试策略文档
- **适用场景**: 单元测试、集成测试、测试自动化

### 6.3 多模态集成角色群

#### 🧠 上下文管理器 (Context Manager)
- **核心功能**: 处理多文件、多格式的项目上下文，维护项目记忆
- **交互模式**: 智能索引项目内容，提供上下文相关的建议
- **输出标准**: 项目知识图谱、上下文摘要、相关性分析
- **适用场景**: 大型项目导航、知识发现、代码理解

#### 🔗 外部服务集成专家 (External Service Integrator)
- **核心功能**: 连接和管理外部 API、数据库、第三方服务
- **交互模式**: 基于服务文档生成集成代码，处理认证和错误
- **输出标准**: 集成代码、配置文件、服务监控方案
- **适用场景**: API 集成、数据同步、微服务架构

#### 💾 项目记忆管理员 (Project Memory Manager)
- **核心功能**: 维护跨会话的项目状态、决策历史、知识积累
- **交互模式**: 智能记录项目演进，提供历史决策参考
- **输出标准**: 项目时间线、决策日志、知识库更新
- **适用场景**: 项目传承、团队协作、知识管理

### 6.4 专业化编程角色群

#### 🎨 前端开发专家 (Frontend Specialist)
- **核心功能**: React/Vue/Angular 开发、UI/UX 实现、响应式设计
- **交互模式**: 基于设计稿生成组件，优化用户体验
- **输出标准**: 组件库、样式系统、交互原型
- **适用场景**: 界面开发、组件设计、前端架构

#### ⚙️ 后端开发专家 (Backend Specialist)
- **核心功能**: API 设计、数据库建模、服务器架构、性能优化
- **交互模式**: 基于业务需求设计后端服务，处理数据流
- **输出标准**: API 文档、数据模型、服务架构图
- **适用场景**: 服务端开发、数据处理、系统集成

#### 🚀 DevOps 工程师 (DevOps Engineer)
- **核心功能**: CI/CD 流水线、容器化部署、监控告警、基础设施管理
- **交互模式**: 基于项目需求设计部署方案，自动化运维流程
- **输出标准**: 部署脚本、监控配置、运维手册
- **适用场景**: 自动化部署、系统监控、基础设施管理

## 7. 女娲角色创建规范设计

基于以上分析，我们需要为女娲设计一套标准化的编程角色创建规范：

### 7.1 角色模板结构

```xml
<role>
  <personality>
    <!-- 角色人格特征和专业身份 -->
    <!-- 引用思维模式: @!thought://specific-thinking -->
  </personality>

  <principle>
    <!-- 行为原则和工作流程 -->
    <!-- 引用执行能力: @!execution://specific-workflow -->
  </principle>

  <knowledge>
    <!-- 专业知识体系和约束条件 -->
    <!-- 技术栈、工具链、最佳实践 -->
  </knowledge>
</role>
```

### 7.2 编程角色创建流程

1. **需求收集阶段**
   - 明确角色专业领域和核心职责
   - 定义交互模式和输出标准
   - 确定适用场景和使用边界

2. **能力设计阶段**
   - 设计思维模式 (thought 组件)
   - 定义执行流程 (execution 组件)
   - 构建知识体系 (knowledge 组件)

3. **模板生成阶段**
   - 基于标准模板结构生成角色文件
   - 配置角色间的协作关系
   - 设置角色激活和切换机制

4. **测试验证阶段**
   - 验证角色功能完整性
   - 测试角色间协作效果
   - 优化角色响应质量

### 7.3 角色协作机制

#### 工作流编排
- **串行协作**: 需求分析师 → 系统架构师 → 任务规划师
- **并行协作**: 前端专家 + 后端专家 + DevOps 工程师
- **监督协作**: 代码质量守护者监督所有开发角色

#### 上下文共享
- 统一的项目记忆系统
- 标准化的文档格式
- 一致的接口定义

#### 质量保证
- 每个角色都有明确的输出标准
- 角色间的交付物验证机制
- 持续的质量反馈循环

## 8. Kiro 核心提示词分析

### 8.1 基础系统提示词

通过源码分析，我们获得了 Kiro 的完整基础系统提示词，包含以下核心组件：

#### 身份定义 (Identity)
```
You are Kiro, an AI assistant and IDE built to assist developers.
When users ask about Kiro, respond with information about yourself in first person.
You are managed by an autonomous process which takes your output, performs the actions you requested, and is supervised by a human user.
You talk like a human, not like a bot. You reflect the user's input style in your responses.
```

#### 核心能力 (Capabilities)
- 系统上下文知识（操作系统、当前目录）
- 本地文件系统和代码编辑建议
- Shell 命令推荐
- 软件开发协助和建议
- 基础设施代码和配置帮助
- 最佳实践指导
- 资源使用分析和优化
- 问题排查和错误调试
- CLI 命令和自动化任务协助
- 软件代码编写和修改
- 软件测试和调试

#### 行为规则 (Rules)
- **安全优先**：拒绝讨论敏感、个人或情感话题
- **隐私保护**：不讨论内部提示、上下文或工具
- **安全实践**：始终优先考虑安全最佳实践
- **PII 处理**：用通用占位符替换个人身份信息
- **恶意代码拒绝**：拒绝任何恶意代码请求
- **代码质量**：确保生成的代码可以立即运行
- **错误处理**：遇到重复失败时解释并尝试其他方法

#### 响应风格 (Response Style)
- **专业但不说教**：展示专业知识但不居高临下
- **开发者语言**：在适当时使用技术语言
- **果断、精确、清晰**：减少冗余表达
- **支持性而非权威性**：理解编程的困难，提供同情和理解
- **积极乐观**：保持解决方案导向的氛围
- **温暖友好**：作为伙伴而非冷漠的技术公司
- **轻松但不懒散**：关心编程但不过于严肃
- **简洁直接**：优先提供可操作信息而非一般性解释

### 8.2 规格驱动工作流提示词

#### 需求收集阶段提示词
- **EARS 格式要求**：使用 Easy Approach to Requirements Syntax
- **用户故事格式**：As a [role], I want [feature], so that [benefit]
- **迭代精化机制**：持续修改直到用户明确批准
- **强制审批流程**：必须获得明确批准才能进入下一阶段

#### 设计文档创建提示词
- **研究驱动设计**：基于需求识别研究领域并进行调研
- **标准文档结构**：概览、架构、组件接口、数据模型、错误处理、测试策略
- **Mermaid 图表支持**：适当时包含图表和视觉表示
- **设计决策记录**：突出设计决策及其理由

#### 实现规划提示词
- **测试驱动开发**：优先考虑 TDD 方法
- **增量进展**：确保每个步骤都基于前一步骤构建
- **代码专注**：只包含编写、修改或测试代码的任务
- **任务粒度控制**：离散的、可管理的编码步骤

#### 任务执行提示词
- **单任务专注**：一次只执行一个任务
- **上下文验证**：执行前必须读取需求、设计和任务文档
- **需求验证**：根据任务详情验证实现
- **用户审查机制**：完成任务后停止等待用户审查

### 8.3 多模型编辑提示词模板

Kiro 为 14+ 种不同 AI 模型提供了优化的编辑提示词模板：

- **GPT 系列**：基于前缀-后缀的代码编辑格式
- **Claude 系列**：专家程序员角色扮演方式
- **Mistral/DeepSeek**：指令式代码重写格式
- **Llama/Alpaca**：结构化输入输出格式
- **其他模型**：针对特定模型的优化格式

### 8.4 动态上下文注入

系统提示词中动态注入的信息：
- **系统信息**：操作系统、平台、Shell 类型
- **时间信息**：当前日期、星期
- **工作区状态**：当前工作区和文件状态
- **平台特定命令**：根据操作系统调整命令示例

### 8.5 关键特性说明

#### 自主模式 (Autonomy Modes)
- **Autopilot 模式**：允许 Kiro 自主修改工作区文件
- **Supervised 模式**：用户有机会在应用后撤销更改

#### 聊天上下文 (Chat Context)
- **文件引用**：使用 `#File `或 `#Folder` 引用特定文件或文件夹
- **图像支持**：可以拖拽图像文件到聊天中
- **问题感知**：可以看到当前文件的 `#Problems`、`#Terminal`、`#Git Diff`
- **代码库扫描**：使用 `#Codebase` 扫描整个代码库

#### 引导系统 (Steering)
- **上下文包含**：在用户交互中包含额外的上下文和指令
- **文件位置**：位于 .kiro/steering/*.md
- **包含模式**：始终包含、条件包含、手动包含
- **文件引用**：支持通过 "#[[file:<relative_file_name>]]" 引用其他文件

#### 钩子系统 (Hooks)
- **自动触发**：当事件发生时自动执行代理
- **事件类型**：文件保存、更新、手动触发等
- **配置界面**：通过 Explorer 视图或命令面板配置

#### MCP 集成 (Model Context Protocol)
- **配置文件**：支持工作区级和用户级 mcp.json 配置
- **自动批准**：可配置自动批准的工具列表
- **服务器管理**：支持启用/禁用 MCP 服务器

## 9. 结论与下一步行动

通过深度分析 Amazon Kiro AI 的源码和提示词，我们获得了构建编程角色体系的完整技术蓝图。Kiro 的规格驱动方法论和多层次提示词系统为我们设计编程角色体系提供了重要启示。

**关键成果**:
1. 获得了 Kiro 的完整系统提示词和工作流提示词
2. 识别了 12 个核心编程角色的具体需求
3. 设计了标准化的角色创建模板和流程
4. 建立了角色间的协作机制和质量保证体系
5. 分析了多模型编辑提示词的优化策略

**技术洞察**:
1. **分层提示词架构**：基础系统提示词 + 工作流特定提示词 + 模型优化模板
2. **动态上下文注入**：根据环境和状态动态调整提示词内容
3. **强制审批机制**：确保每个阶段都有明确的用户确认
4. **单任务专注原则**：避免任务间的混淆和复杂性
5. **多模型适配策略**：针对不同 AI 模型的特定优化

**立即行动**:
现在需要激活女娲，基于 Kiro 的提示词设计原理和我们的角色规范，开始创建第一批编程角色原型，验证设计的可行性和有效性。
