---
人员: 
  - "[[马大伟]]"
tags:
  - articles
日期: 2021-04-27
时间: 2024-09-19 05:33:15.350905+00:00
相关:
  - "[[Beancount]]"
  - "[[CSS]]"
  - "[[custom.css]]"
  - "[[Eisenhower matrix]]"
  - "[[Emacs Org Mode]]"
  - "[[GTD]]"
  - "[[Key Results]]"
  - "[[Logseq]]"
  - "[[Markdown]]"
  - "[[Note]]"
  - "[[Objectives]]"
  - "[[OKR]]"
  - "[[OmniFocus]]"
  - "[[Roam Research]]"
  - "[[Tiddlywiki]]"
  - "[[TODO]]"
  - "[[Workflowy]]"
  - "[[个人愿景]]"
  - "[[人生管理系统]]"
  - "[[任务管理]]"
  - "[[反向链接]]"
  - "[[图数据库]]"
  - "[[家庭财务]]"
  - "[[影响力]]"
  - "[[打卡次数]]"
  - "[[技术]]"
  - "[[投资]]"
  - "[[时间四象限]]"
  - "[[时间管理]]"
  - "[[时间追踪]]"
  - "[[标签]]"
  - "[[生产力与自我提升]]"
  - "[[知识图谱]]"
  - "[[知识宫殿]]"
  - "[[被动收入]]"
  - "[[资金统计]]"
  - "[[邮件订阅]]"
  - "[[高级查询]]"

链接: https://www.bmpi.dev/self/okr-gtd-note-logseq/
附件: https://img.bmpi.dev/d33c93b6-734c-cf47-a3cf-866ccfd29872.png)
---
## Document Note

## Summary

本文分享我使用Logseq实现人生管理系统的OKR、GTD与Note的功能。

## Full Document
[[Full Document Contents/Articles/马大伟-OKR + GTD + Note = Logseq.md|See full document content →]]

## Highlights
- 在 [我的人生管理系统](https://www.bmpi.dev/self/life-in-plain-text/) 这篇文章中我分享了如何从个人愿景出发设定个人OKR ，将OKR使用 [GTD](https://www.bmpi.dev/tags/%E6%97%B6%E9%97%B4%E7%AE%A1%E7%90%86/) 的方法拆解成多个TODO ，这些TODO所产生的笔记通过反向链接关联起来。 ([View Highlight](https://read.readwise.io/read/01j84c32r4eyjknzpgf2vy54hy))
    - 卧槽，我之前初学 tana 的时候就想到过 愿景管理！ #肯定 #闪念 
- Mission -> Vision -> Strategy -> Objectives -> Key Results ([View Highlight](https://read.readwise.io/read/01j84c4jxd9gswxjsm28xkfpch))
- OKR是一个从个人愿景到设定个人目标的方法论，它能帮助组织成员/使用者聚焦最重要的事。OKR是确保将整个组织/个人的力量都聚焦于完成对所有人/个人都同样重要的事项的一套管理方法。正是多了Objectives，才能让参与者先思考最重要的事，然后再去拆解任务。而KPI却是直接确定任务。 ([View Highlight](https://read.readwise.io/read/01j84c58tyaz1m2v0mb5c4ewgn))
- 让我们先把这个流程转变为一些问题域，之后再寻找这些问题域的Logseq的解空间。这个流程需要解决以下几个问题：
  • 如何确定个人愿景？
  • 个人愿景如何拆解为Objectives？
  • Objective如何拆解为Key Result？
  • Key Result如何拆解为TODO？
  • 如何追踪Objective耗费的时间与资金 ([View Highlight](https://read.readwise.io/read/01j84c6n8h3b6tak8fyg5w21xn))
- 个人愿景如何拆解为Objectives
  从个人愿景出发我设定了四个长期关注的纬度：健康、能力、收入与影响力。这四个纬度又可以设定以下的Objectives：
  • 身体提升：拥有一个健康的身体；
  • 能力提升：提升技术到价值的转化效率；
  • 收入提升：家庭财务状况健康；
  • 影响力提升：在技术和投资领域建立专业影响力。 ([View Highlight](https://read.readwise.io/read/01j84c8a2eg33qre844dnnxehx))
- Objectives相当于以期望未来能达成的结果而设定的大的目标，此阶段并不需要具体化。 ([View Highlight](https://read.readwise.io/read/01j84c8rhq102m7vzr2w7fqndk))
- Objective如何拆解为Key Result
  Objective设定后需要拆解为多个Key Result。Key Result需要以目前能实现的程度而设定，如果设定的太庞大，根本实现不了，那就没有意义了。以下是我根据当前我的能力范围而设定的一些Key Result：
  • 2021-OKR-O1-KRs
  • 每月熬夜不超过4次#2021-OKR-O1-KR-1
  • 减肥5斤#2021-OKR-O1-KR-2
  • 2021-OKR-O3-KRs
  • 被动收入投资组合年复合收益率超15% `#2021-OKR-O3-KR-1`
  • 2021-OKR-O4-KRs
  • 在博客技术与投资领域分别输出超过10篇文章#2021-OKR-O4-KR-1 ([View Highlight](https://read.readwise.io/read/01j84c9v4wykcf73fxzafpkan8))
- 类似2021-OKR-O1-KRs这种在Logseq是一个独立的页面，#2021-OKR-O1-KR-1是一个标签（Tag），点击这个标签进入的就是一个2021-OKR-O1-KR-1的页面。这个页面就是此Key Result的页面，在这个页面中可以查询到所有用此页面标题作为标签的TODO任务。 ([View Highlight](https://read.readwise.io/read/01j84cacpdbm1kc83ty5t4ap6p))
