---
人员: 
  - "[[flomoapp.com]]"
tags:
  - articles
日期: None
时间: None
链接: https://help.flomoapp.com/basic/tag.html
附件:
---
## Document Note

## Summary

通过标签来将 MEMO 之间建立联系。本功能可免费使用

## Full Document
flomo 的多级标签，不同于文件夹和传统标签。既保留了标签的灵活性，又兼顾了文件夹的结构体系。

![](https://resource.flomoapp.com/101/tagshowcase.png!webp)
flomo 的标签格式为 **`#标签` 。多个标签之间记得加空格。**

在 flomo 输入时，你可以随时通过点击 flomo 的输入框下方的「#」按钮，或通过键盘直接输入「#」号，来插入一个标签，例如 `#标签` （记得以空格结尾）。

输入完成后点击发送按钮，即可看到标签创建成功。

flomo 的标签系统比较特别，不但支持层级，还能在正文中当做内容使用。 至于标签放在开头还是结尾，以及是否放在 MEMO 中间，这个并无标准，顺手就好。

> 注意：
> 
>  * 请不要在标签中使用 emoji 以及各种特殊字符，会导致无法查询。如`#😁开心`或`#读书&学习`。
> * 如果有英文单词，之间请用连接符如 #Apple\_seed
> * 标签与标签之间，需要有一个空格分割，否则会被识别为一个标签。
> 

flomo 支持 `#标签/子标签` 这样的多层级方式，这样既能兼顾标签的便利性，又能方便浏览。比如我们读了一本书《Different》，想把它放在 `#读书`标签下，并且还保留几个书中的章节，那么我们就可以写作：

`#Books/Different/营销近视症`这样就相当于创建了三个层级的标签

![](https://resource.flomoapp.com/101/images-361.png!webp)
![](https://s1.ax1x.com/2021/12/11/oTPVWn.gif)
不知道如何规划标签？查看[用标签，让结构生长](https://help.flomoapp.com/method/use-tag.html)

修改：光标移动至标签处悬停，点击标签右侧「···」，选择「重命名」即可对标签完成修改。 删除：点击删除标签，**即会删除标签下所有内容**

![tag_rename_web](https://resource.flomoapp.com/101/tag_rename_web.png!webp)
修改：在标签列表中找到想要修改的标签，点击标签右侧「···」，在菜单中选中「重命名」，即可完成修改。 删除：点击删除标签，**即会删除标签下所有内容**

![tag_rename_phone](https://resource.flomoapp.com/101/tag_rename_phone.png!webp)

> 💡提示：若想将「标签 A」放至另一个「标签 B」之下，可以在重命名框中输入 `#标签B/标签A`，即可完成标签移动
> 
> 

目前 flomo 的标签按照**字母表顺序排序**。

如果想要修改顺序，可以在标签前面增加数字，如从`#标签`改为`#01_标签`。

拖动和其他排序方式尚在思考，请诸位耐心等待。

#### [#](https://help.flomoapp.com/basic/tag.html#如何找到未添加标签的内容) **如何找到未添加标签的内容**

点击搜索框即可弹出「无标签」搜索选项

![](https://resource.flomoapp.com/101/images-2.png!webp)
#### [#](https://help.flomoapp.com/basic/tag.html#如何在标签中使用-emoji) **如何在标签中使用 Emoji**

使用 Emoji 可以让的标签更加醒目和特别。在 flomo 的网页端和安卓端，目前支持点击标签前面的图标进行更换。仅限 `PRO` 用户使用。

![](https://resource.flomoapp.com/101/images-406.png!webp)
